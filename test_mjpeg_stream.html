<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MJPEG Stream Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .controls {
            background-color: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }

        input, select, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #444;
            border-radius: 4px;
            background-color: #374151;
            color: white;
        }

        button {
            background-color: #FFFFFF;
            color: black;
            border: 1px solid #000000;
            cursor: pointer;
            font-weight: bold;
        }

        button:hover {
            background-color: #F8F9FA;
        }

        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }

        .stream-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .stream-cell {
            background-color: #2d2d2d;
            border-radius: 8px;
            overflow: hidden;
            min-height: 300px;
        }

        .stream-header {
            background-color: #374151;
            padding: 10px;
            font-weight: bold;
            text-align: center;
        }

        .stream-container {
            position: relative;
            height: 250px;
            background-color: #000;
        }

        .stream-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .stream-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6b7280;
            font-size: 14px;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .status.success {
            background-color: #065f46;
            border-left: 4px solid #10b981;
        }

        .status.error {
            background-color: #7f1d1d;
            border-left: 4px solid #ef4444;
        }

        .status.info {
            background-color: #1e3a8a;
            border-left: 4px solid #3b82f6;
        }

        .api-info {
            background-color: #374151;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MJPEG Stream Test</h1>
            <p>Test the new RTSP to MJPEG streaming backend</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>Backend URL:</label>
                <input type="text" id="backendUrl" value="http://localhost:8000" style="width: 300px;">
            </div>

            <div class="control-group">
                <label>RTSP URL:</label>
                <input type="text" id="rtspUrl" value="rtsp://admin:Admin@123@*************:554" style="width: 400px;">
            </div>

            <div class="control-group">
                <label>Stream ID:</label>
                <input type="text" id="streamId" value="test_stream" style="width: 200px;">
            </div>

            <div class="control-group">
                <button onclick="checkHealth()">Check Server Health</button>
                <button onclick="startStream()">Start Stream</button>
                <button onclick="stopStream()">Stop Stream</button>
                <button onclick="listStreams()">List Active Streams</button>
            </div>

            <div class="control-group">
                <label>Collection:</label>
                <select id="collectionSelect">
                    <option value="">Select Collection</option>
                </select>
                <button onclick="loadCollections()">Load Collections</button>
                <button onclick="startCollectionStreams()">Start Collection Streams</button>
            </div>
        </div>

        <div id="status"></div>

        <div class="stream-grid" id="streamGrid">
            <!-- Streams will be added here dynamically -->
        </div>

        <div class="api-info">
            <h3>API Endpoints:</h3>
            <ul>
                <li><strong>Health Check:</strong> GET /api/health</li>
                <li><strong>Start Stream:</strong> POST /api/start_stream</li>
                <li><strong>Stop Stream:</strong> DELETE /api/stop_stream/{stream_id}</li>
                <li><strong>Video Feed:</strong> GET /api/video_feed/{stream_id}</li>
                <li><strong>List Streams:</strong> GET /api/streams</li>
                <li><strong>Start Collection:</strong> GET /api/start_collection_streams/{collection_name}</li>
            </ul>
        </div>
    </div>

    <script>
        function getBackendUrl() {
            return document.getElementById('backendUrl').value;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkHealth() {
            try {
                const response = await fetch(`${getBackendUrl()}/api/health`);
                const data = await response.json();
                showStatus(`Server is healthy. Active streams: ${data.active_streams}`, 'success');
                console.log('Health check:', data);
            } catch (error) {
                showStatus(`Server health check failed: ${error.message}`, 'error');
                console.error('Health check error:', error);
            }
        }

        async function startStream() {
            const rtspUrl = document.getElementById('rtspUrl').value;
            const streamId = document.getElementById('streamId').value;

            if (!rtspUrl || !streamId) {
                showStatus('Please enter both RTSP URL and Stream ID', 'error');
                return;
            }

            try {
                const response = await fetch(`${getBackendUrl()}/api/start_stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rtsp_url: rtspUrl,
                        stream_id: streamId
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showStatus(`Stream started successfully: ${data.feed_url}`, 'success');
                    addStreamToGrid(streamId, data.feed_url);
                } else {
                    showStatus(`Failed to start stream: ${data.error}`, 'error');
                }
                console.log('Start stream response:', data);
            } catch (error) {
                showStatus(`Error starting stream: ${error.message}`, 'error');
                console.error('Start stream error:', error);
            }
        }

        async function stopStream() {
            const streamId = document.getElementById('streamId').value;

            if (!streamId) {
                showStatus('Please enter Stream ID', 'error');
                return;
            }

            try {
                const response = await fetch(`${getBackendUrl()}/api/stop_stream/${streamId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();
                if (data.success) {
                    showStatus(`Stream stopped successfully`, 'success');
                    removeStreamFromGrid(streamId);
                } else {
                    showStatus(`Failed to stop stream: ${data.error}`, 'error');
                }
                console.log('Stop stream response:', data);
            } catch (error) {
                showStatus(`Error stopping stream: ${error.message}`, 'error');
                console.error('Stop stream error:', error);
            }
        }

        async function listStreams() {
            try {
                const response = await fetch(`${getBackendUrl()}/api/streams`);
                const data = await response.json();

                if (data.streams) {
                    const streamCount = Object.keys(data.streams).length;
                    showStatus(`Found ${streamCount} active streams`, 'success');

                    // Clear and repopulate grid
                    const grid = document.getElementById('streamGrid');
                    grid.innerHTML = '';

                    Object.entries(data.streams).forEach(([streamId, streamInfo]) => {
                        addStreamToGrid(streamId, streamInfo.feed_url);
                    });
                } else {
                    showStatus('No active streams found', 'info');
                }
                console.log('List streams response:', data);
            } catch (error) {
                showStatus(`Error listing streams: ${error.message}`, 'error');
                console.error('List streams error:', error);
            }
        }

        async function loadCollections() {
            try {
                const response = await fetch(`${getBackendUrl()}/collections`);
                const data = await response.json();

                const select = document.getElementById('collectionSelect');
                select.innerHTML = '<option value="">Select Collection</option>';

                if (data.collections) {
                    data.collections.forEach(collection => {
                        const option = document.createElement('option');
                        option.value = collection;
                        option.textContent = collection;
                        select.appendChild(option);
                    });
                    showStatus(`Loaded ${data.collections.length} collections`, 'success');
                } else {
                    showStatus('No collections found', 'info');
                }
                console.log('Collections:', data);
            } catch (error) {
                showStatus(`Error loading collections: ${error.message}`, 'error');
                console.error('Load collections error:', error);
            }
        }

        async function startCollectionStreams() {
            const collection = document.getElementById('collectionSelect').value;

            if (!collection) {
                showStatus('Please select a collection', 'error');
                return;
            }

            try {
                const response = await fetch(`${getBackendUrl()}/api/start_collection_streams/${collection}`);
                const data = await response.json();

                if (data.started_streams) {
                    showStatus(`Started ${data.started_streams.length} streams for collection: ${collection}`, 'success');

                    // Clear and add streams to grid
                    const grid = document.getElementById('streamGrid');
                    grid.innerHTML = '';

                    data.started_streams.forEach(stream => {
                        addStreamToGrid(stream.stream_id, stream.feed_url, stream.camera_ip);
                    });

                    if (data.errors && data.errors.length > 0) {
                        console.warn('Stream errors:', data.errors);
                    }
                } else {
                    showStatus(`Failed to start collection streams: ${data.error}`, 'error');
                }
                console.log('Collection streams response:', data);
            } catch (error) {
                showStatus(`Error starting collection streams: ${error.message}`, 'error');
                console.error('Collection streams error:', error);
            }
        }

        function addStreamToGrid(streamId, feedUrl, cameraIp = null) {
            const grid = document.getElementById('streamGrid');

            const streamCell = document.createElement('div');
            streamCell.className = 'stream-cell';
            streamCell.id = `stream-${streamId}`;

            const displayName = cameraIp || streamId;
            const fullUrl = feedUrl.startsWith('http') ? feedUrl : `${getBackendUrl()}${feedUrl}`;

            streamCell.innerHTML = `
                <div class="stream-header">${displayName}</div>
                <div class="stream-container">
                    <img class="stream-img" src="${fullUrl}" alt="Stream ${streamId}"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                         onload="this.style.display='block'; this.nextElementSibling.style.display='none';">
                    <div class="stream-placeholder">Loading stream...</div>
                </div>
            `;

            grid.appendChild(streamCell);
        }

        function removeStreamFromGrid(streamId) {
            const streamElement = document.getElementById(`stream-${streamId}`);
            if (streamElement) {
                streamElement.remove();
            }
        }

        // Initialize
        window.onload = function() {
            checkHealth();
            loadCollections();
        };
    </script>
</body>
</html>

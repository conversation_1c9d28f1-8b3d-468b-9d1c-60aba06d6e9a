.dashboard {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #FFFFFF, #F8F9FA); /* Using white gradient */
  padding: 1rem;
  width: 100%;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* Using black with opacity */
}

.manage-collections-btn {
  background: linear-gradient(135deg, #000000, #343A40); /* Using black gradient */
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem; /* Using ravia border-radius */
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2); /* Using black glow */
}

.manage-collections-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(0, 229, 255, 0.5);
}

.camera-search-container {
  flex: 1;
  max-width: 400px;
  margin: 0 1rem;
}

.camera-search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(0, 229, 255, 0.2);
  border-radius: 0.75rem;
  font-size: 1rem;
  transition: all 0.2s;
  background: rgba(0, 0, 0, 0.2);
  color: white;
  backdrop-filter: blur(5px);
}

.camera-search-input:focus {
  border-color: #00E5FF; /* Using ravia-cyan-500 */
  outline: none;
  box-shadow: 0 0 15px rgba(0, 229, 255, 0.3);
}

.camera-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.layout-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.dashboard-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.9); /* Using white with opacity */
  border-radius: 0.75rem;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1); /* Using black shadow */
  overflow: hidden;
  padding: 1px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.dashboard-main {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-container {
  height: 100%;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.8);
  font-size: 1.2rem;
}

.bookmark-container {
  height: 100%;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.8);
  font-size: 1.2rem;
}

@media (max-width: 1200px) {
  .dashboard-content {
    flex-direction: column;
  }

  .dashboard-maps {
    flex-direction: row;
    min-width: 100%;
  }
}

.collections-sidebar {
  width: 200px;
  background: linear-gradient(to bottom, #FFFFFF, #F8F9FA); /* Using white gradient */
  padding: 20px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.collections-sidebar h3 {
  color: #000000;
  margin-bottom: 20px;
}

.collections-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.collections-sidebar li {
  color: rgba(255, 255, 255, 0.8);
  padding: 10px;
  cursor: pointer;
  border-radius: 0.75rem;
  margin-bottom: 5px;
  transition: all 0.2s;
}

.collections-sidebar li:hover {
  color: #fff;
  background: transparent;
  box-shadow: none;
}

.collections-sidebar li.active {
  color: #fff;
  font-weight: bold;
  background: linear-gradient(135deg, rgba(0, 229, 255, 0.2), rgba(122, 50, 255, 0.2));
  border: 1px solid rgba(0, 229, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 229, 255, 0.3);
}

.streams-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.streams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
  height: 100%;
  width: 100%;
}

.stream-item {
  aspect-ratio: 16/9;
  background-color: #FFFFFF; /* Using white */
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.stream-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  border-color: rgba(0, 0, 0, 0.2);
}

.stream-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  color: black;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.stream-name {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loading,
.error,
.no-streams {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #000000;
  font-size: 1.2em;
}

.error {
  color: #DC3545; /* Using red for errors */
}

.no-collection-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2em;
  color: rgba(0, 0, 0, 0.6);
  text-align: center;
  padding: 20px;
}

.no-bookmarks-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(30, 11, 56, 0.9);
  border-radius: 0.75rem;
  padding: 20px;
  text-align: center;
  color: #fff;
  max-width: 400px;
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 229, 255, 0.2);
  box-shadow: 0 0 15px rgba(122, 50, 255, 0.3);
}

.no-bookmarks-message p {
  margin: 10px 0;
}

.no-bookmarks-message p:first-child {
  font-size: 1.2em;
  font-weight: 500;
  color: #00E5FF; /* Using ravia-cyan-500 */
}
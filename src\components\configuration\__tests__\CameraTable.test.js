import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CameraTable from '../CameraTable';
import cameraApi from '../../../services/cameraApi';

// Mock the camera API
jest.mock('../../../services/cameraApi');

describe('CameraTable', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    // Mock API to return a promise that doesn't resolve immediately
    cameraApi.getCamerasWithStatus.mockReturnValue(new Promise(() => {}));

    render(<CameraTable />);
    
    expect(screen.getByText('Loading cameras...')).toBeInTheDocument();
  });

  test('renders camera data when loaded successfully', async () => {
    const mockCameras = [
      {
        id: 'eagle_*************',
        name: 'eagle (*************)',
        ip: '*************',
        collection: 'eagle',
        streamUrl: 'rtsp://admin:Admin@123@*************:554',
        streamId: 'eagle_*************',
        status: 'Active',
        isActive: true,
        feedUrl: 'http://localhost:8000/api/video_feed/eagle_*************'
      },
      {
        id: 'karnal_*************',
        name: 'karnal (*************)',
        ip: '*************',
        collection: 'karnal',
        streamUrl: 'rtsp://admin:asgnert@*************:554',
        streamId: 'karnal_*************',
        status: 'Inactive',
        isActive: false,
        feedUrl: null
      }
    ];

    cameraApi.getCamerasWithStatus.mockResolvedValue(mockCameras);

    render(<CameraTable />);

    await waitFor(() => {
      expect(screen.getByText('Camera Overview')).toBeInTheDocument();
    });

    // Check if camera data is displayed
    expect(screen.getByText('eagle (*************)')).toBeInTheDocument();
    expect(screen.getByText('karnal (*************)')).toBeInTheDocument();
    expect(screen.getByText('*************')).toBeInTheDocument();
    expect(screen.getByText('*************')).toBeInTheDocument();
    expect(screen.getByText('eagle')).toBeInTheDocument();
    expect(screen.getByText('karnal')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  test('renders error state when API fails', async () => {
    cameraApi.getCamerasWithStatus.mockRejectedValue(new Error('API Error'));

    render(<CameraTable />);

    await waitFor(() => {
      expect(screen.getByText(/Failed to load camera data/)).toBeInTheDocument();
    });

    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  test('renders empty state when no cameras are configured', async () => {
    cameraApi.getCamerasWithStatus.mockResolvedValue([]);

    render(<CameraTable />);

    await waitFor(() => {
      expect(screen.getByText(/No cameras configured/)).toBeInTheDocument();
    });
  });
});

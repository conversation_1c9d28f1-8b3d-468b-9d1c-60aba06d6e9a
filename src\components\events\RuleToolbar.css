.rule-toolbar {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
}

.rule-toolbar-header {
  margin-bottom: 6px;
}

.rule-toolbar-header h3 {
  font-size: 14px;
  margin-bottom: 2px;
  color: #666666;
  font-weight: 600;
}

.rule-toolbar-header p {
  font-size: 11px;
  color: #666666;
  margin: 0;
}

.rule-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 6px;
  margin-bottom: 8px;
}

.rule-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rule-button.enabled {
  background-color: #f8f9fa;
  color: #666666;
  border: 1px solid #e0e0e0;
}

.rule-button.enabled:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rule-button.disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
  border: 1px solid #dee2e6;
}

.rule-button.selected {
  background-color: #666666;
  color: #ffffff;
  border: 1px solid #666666;
}

.rule-button.selected:hover {
  background-color: #888888;
}

.selected-rules {
  margin-top: 8px;
  padding: 6px;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
  border: 1px solid #e0e0e0;
}

.selected-rules > span {
  font-weight: 600;
  color: #666666;
  margin-right: 4px;
  font-size: 11px;
}

.no-rules-selected {
  color: #999999;
  font-style: italic;
  font-size: 11px;
}

.selected-rule-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.rule-tag {
  background-color: #666666;
  color: #ffffff;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.remove-rule {
  background: none;
  border: none;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 6px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-rule:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

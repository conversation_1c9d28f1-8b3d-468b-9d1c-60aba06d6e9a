.event-statistics {
  background-color: #ffffff;
  color: #000000;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
}

.event-statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.event-statistics-header h2 {
  font-size: 1.5rem;
  margin: 0;
  color: #000000;
}

.chart-type-selector {
  display: flex;
  gap: 10px;
}

.chart-type-btn {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #e0e0e0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chart-type-btn.active {
  background-color: #000000;
  color: #ffffff;
}

.chart-type-btn:hover:not(.active) {
  background-color: #f5f5f5;
}

.event-statistics-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.rules-filter,
.camera-filter {
  flex: 1;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.rules-filter h3,
.camera-filter h3 {
  font-size: 1rem;
  margin-top: 0;
  margin-bottom: 10px;
  color: #000000;
}

.rules-list {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 10px;
}

.rule-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.rule-checkbox input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #000000;
}

.camera-filter select {
  width: 100%;
  padding: 8px;
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.event-statistics-visualization {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  min-height: 300px;
  border: 1px solid #e0e0e0;
}

.no-data-message {
  color: #666666;
  font-size: 1.1rem;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pie-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.pie-chart svg {
  width: 200px;
  height: 200px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.bar-chart {
  width: 100%;
}

.bars-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bar-label {
  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bar-container {
  flex: 1;
  height: 24px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px solid #dee2e6;
}

.bar {
  height: 100%;
  transition: width 0.3s ease;
}

.bar-value {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #000000;
  font-weight: 500;
  text-shadow: none;
}

.event-statistics-loading,
.event-statistics-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2rem;
  color: #666666;
}

.event-statistics-error {
  color: #dc3545;
}

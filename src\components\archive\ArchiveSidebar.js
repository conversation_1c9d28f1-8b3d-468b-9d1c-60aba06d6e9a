// React must be in scope when using JSX
import React, { useState, useEffect, useRef } from 'react';
import './ArchiveSidebar.css';
import '../sidebar/ModernSidebar.css'; // Import the new modern sidebar styles
import {
  Archive as ArchiveIcon,
  Assessment as RecReportIcon,
  Warning as CriticalIcon,
  PlayArrow as PlaybackIcon
} from '@mui/icons-material';

const ArchiveSidebar = ({ onMenuSelect }) => {
  const [activeItem, setActiveItem] = useState('archive-playback');
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Ref for the sidebar element
  const sidebarRef = useRef(null);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Reset expanded state when switching between mobile and desktop
      if (!mobile) {
        setSidebarExpanded(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle sidebar expansion for mobile view
  const toggleSidebar = () => {
    setSidebarExpanded(prev => !prev);
  };

  // Handle menu item selection
  useEffect(() => {
    if (onMenuSelect) {
      onMenuSelect(activeItem);
    }
  }, [activeItem, onMenuSelect]);

  const menuItems = [
    { id: 'archive-playback', label: 'Archive Playback', icon: <ArchiveIcon /> },
    { id: 'recording-report', label: 'Recording Report', icon: <RecReportIcon /> },
    { id: 'critical-video', label: 'Critical Video', icon: <CriticalIcon /> },
    { id: 'redundant-playback', label: 'Redundant Playback', icon: <PlaybackIcon /> }
  ];

  return (
    <>
      {isMobile && (
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={sidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarExpanded ? "✕" : "☰"}
        </button>
      )}
      <div
        ref={sidebarRef}
        className="universal-sidebar-content"
        role="navigation"
        aria-label="Archive navigation"
      >
        {/* Menu items */}
        {menuItems.map((item) => (
          <div key={item.id} className="sidebar-section">
            <button
              className={`sidebar-btn ${activeItem === item.id ? 'active' : ''}`}
              onClick={() => setActiveItem(item.id)}
            >
              <span className="sidebar-icon">{item.icon}</span>
              <span>{item.label}</span>
              {isMobile && !sidebarExpanded && (
                <span className="sidebar-tooltip">{item.label}</span>
              )}
            </button>
          </div>
        ))}

      {/* No recordings section in sidebar anymore */}
      </div>
    </>
  );
};

// Use React.memo to optimize rendering
export default React.memo(ArchiveSidebar);
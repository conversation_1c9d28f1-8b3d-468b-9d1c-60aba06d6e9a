.rules-on-camera {
  padding: 12px;
  background-color: #ffffff;
  border-radius: 6px;
  color: #666666;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
}

.rules-on-camera-header {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.rules-on-camera-header h2 {
  font-size: 18px;
  margin-bottom: 4px;
  color: #666666;
  font-weight: 600;
}

.rules-on-camera-header p {
  font-size: 12px;
  color: #666666;
  margin: 0;
}

.rules-on-camera-filters {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
  flex-wrap: nowrap;
  flex-shrink: 0;
}

.search-filter {
  flex: 1;
  min-width: 120px;
}

.search-input {
  width: 100%;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  color: #666666;
  font-size: 12px;
}

.area-filter {
  width: 120px;
}

.area-select {
  width: 100%;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  color: #666666;
  font-size: 12px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 6px center;
  background-size: 12px;
}

.select-all-container {
  display: flex;
  align-items: center;
}

.select-all-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 12px;
}

.select-all-checkbox {
  margin-right: 4px;
  width: 14px;
  height: 14px;
  accent-color: #666666;
}

.filter-apply-button {
  padding: 6px 12px;
  background-color: #666666;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.filter-apply-button:hover {
  background-color: #888888;
}

.filter-apply-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.success-message {
  background-color: rgba(182, 225, 75, 0.2);
  border-left: 4px solid #b6e14b;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #b6e14b;
}

.error-message {
  background-color: rgba(244, 67, 54, 0.2);
  border-left: 4px solid #f44336;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #f44336;
}

.rules-on-camera-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #aaa;
}

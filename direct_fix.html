<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Direct Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1 {
            color: #1565c0;
            border-bottom: 2px solid #1565c0;
            padding-bottom: 10px;
        }
        .info {
            background-color: #e3f2fd;
            border-left: 4px solid #1565c0;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .button {
            background-color: #1565c0;
            border: none;
            color: white;
            padding: 12px 24px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 20px 0;
            cursor: pointer;
            border-radius: 4px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #0d47a1;
        }
        .button-secondary {
            background-color: #757575;
            margin-left: 10px;
        }
        .button-secondary:hover {
            background-color: #616161;
        }
        pre {
            background-color: #263238;
            color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
        }
        .steps {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .steps h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .steps ol {
            margin-bottom: 0;
        }
        .bookmarklet {
            display: inline-block;
            padding: 10px 15px;
            background-color: #ffab00;
            color: #000;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .bookmarklet:hover {
            background-color: #ff9100;
        }
    </style>
</head>
<body>
    <h1>VMS Direct Fix Tool</h1>
    
    <div class="info">
        <h2>ℹ️ About This Tool</h2>
        <p>This tool attempts to directly fix the test_collection issue by:</p>
        <ul>
            <li>Finding and modifying React component state in the running application</li>
            <li>Removing test_collection entries from the UI without a full reset</li>
            <li>Forcing a re-render of the affected components</li>
        </ul>
        <p>This is a more targeted approach than the force reset tool.</p>
    </div>
    
    <div class="steps">
        <h3>How to use this tool:</h3>
        <ol>
            <li>Open your VMS application in the browser</li>
            <li>Drag the bookmarklet below to your bookmarks bar</li>
            <li>Click the bookmarklet while viewing your VMS application</li>
            <li>Alternatively, copy the script below and paste it in your browser's console</li>
        </ol>
    </div>
    
    <h3>Bookmarklet:</h3>
    <p>Drag this link to your bookmarks bar:</p>
    <a id="bookmarklet" class="bookmarklet" href="#">Fix Test Collection</a>
    
    <h3>Or run directly:</h3>
    <button id="runButton" class="button">Run Direct Fix</button>
    <button id="copyButton" class="button button-secondary">Copy Script to Clipboard</button>
    
    <h3>Script Code:</h3>
    <pre id="codeSnippet"></pre>
    
    <script>
        // Load the script content
        fetch('direct_fix.js')
            .then(response => response.text())
            .then(text => {
                document.getElementById('codeSnippet').textContent = text;
                
                // Create bookmarklet
                const bookmarkletCode = `javascript:(function(){${text.replace(/\n/g, ' ')}})();`;
                document.getElementById('bookmarklet').href = bookmarkletCode;
                
                // Set up copy button
                document.getElementById('copyButton').addEventListener('click', function() {
                    navigator.clipboard.writeText(text).then(() => {
                        alert('Script copied to clipboard! Paste it in your browser console.');
                    }).catch(err => {
                        console.error('Could not copy text: ', err);
                        alert('Failed to copy. Please select and copy the script manually.');
                    });
                });
                
                // Set up run button
                document.getElementById('runButton').addEventListener('click', function() {
                    if (confirm("Run the direct fix script now?")) {
                        try {
                            eval(text);
                        } catch (e) {
                            console.error('Error running script:', e);
                            alert(`Error running script: ${e.message}`);
                        }
                    }
                });
            })
            .catch(error => {
                document.getElementById('codeSnippet').textContent = 
                    `Error loading script: ${error}\n\nPlease make sure direct_fix.js is in the same directory.`;
            });
    </script>
</body>
</html>

.add-camera-form {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-header h3 {
  margin: 0;
  color: #fff;
  font-size: 1.2rem;
}

.close-button {
  background: transparent;
  border: none;
  color: #888;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  color: #ccc;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.form-group input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #444;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 0.9rem;
}

.form-group input:focus {
  outline: none;
  border-color: #2196F3;
}

.form-group input::placeholder {
  color: #666;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.primary-button,
.delete-button,
.cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.primary-button {
  background: #2196F3;
  color: white;
}

.primary-button:hover {
  background: #1976D2;
}

.delete-button {
  background: #f44336;
  color: white;
}

.delete-button:hover {
  background: #d32f2f;
}

.cancel-button {
  background: #666;
  color: white;
}

.cancel-button:hover {
  background: #555;
}

.error-message {
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  margin-bottom: 15px;
  white-space: pre-line;
}

.validation-status {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-status::before {
  content: "⏳";
  animation: spin 1s linear infinite;
}

.validation-success {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.primary-button:disabled,
.delete-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Delete Confirmation Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
}

.modal-content h3 {
  color: #fff;
  margin: 0 0 15px 0;
}

.modal-content p {
  color: #ccc;
  margin: 0 0 20px 0;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.confirm-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background: #f44336;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-button:hover {
  background: #d32f2f;
}

.add-camera-form-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.show-form-button {
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s;
}

.show-form-button:hover {
  background: #218838;
}

.collection-select {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.collection-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}
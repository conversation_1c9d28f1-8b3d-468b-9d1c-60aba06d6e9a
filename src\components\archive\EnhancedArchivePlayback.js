import React, { useState, useRef, useEffect } from 'react';
import { useCameraStore } from '../../store/cameraStore';
import { useArchiveStore } from '../../store/archiveStore';
import archiveApi from '../../services/archiveApi';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Calendar, 
  Filter, 
  Search, 
  Grid, 
  List, 
  ChevronLeft, 
  Clock, 
  HardDrive, 
  Video, 
  CheckCircle
} from 'lucide-react';

// Helper function to format date
const formatDate = (timestamp) => {
  if (!timestamp) return 'Unknown';
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

const EnhancedArchivePlayback = ({ onSelectRecording, selectedRecordingId }) => {
  const { cameras } = useCameraStore();
  const {
    recordings,
    availableStreams,
    isLoading,
    error,
    filters,
    selectedStreamId,
    loadRecordings,
    loadAvailableStreams,
    setFilters,
    getFilteredRecordings,
    clearError,
    startStatusPolling,
    stopStatusPolling,
    restartRecordings
  } = useArchiveStore();

  // Enhanced video player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [filterDate, setFilterDate] = useState('today');
  const [filterCamera, setFilterCamera] = useState('all');
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [selectionStart, setSelectionStart] = useState(null);
  const [selectionEnd, setSelectionEnd] = useState(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [downloadFormat, setDownloadFormat] = useState('mp4');
  const [filteredRecordings, setFilteredRecordings] = useState([]);
  
  const videoRef = useRef(null);
  const timelineRef = useRef(null);

  // Load available streams and start status polling on component mount
  useEffect(() => {
    loadAvailableStreams();
    startStatusPolling();
    return () => {
      stopStatusPolling();
    };
  }, [loadAvailableStreams, startStatusPolling, stopStatusPolling]);

  // Update filtered recordings when recordings or filters change
  useEffect(() => {
    const filtered = getFilteredRecordings();
    setFilteredRecordings(filtered);
  }, [recordings, filters, getFilteredRecordings]);

  // Enhanced video player functions
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const formatTime = (seconds) => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleTimelineClick = (e) => {
    if (!timelineRef.current || !videoRef.current) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;
    
    if (isSelecting) {
      if (selectionStart === null) {
        setSelectionStart(newTime);
      } else if (selectionEnd === null) {
        setSelectionEnd(newTime);
        setIsSelecting(false);
      }
    } else {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const handleVideoTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleVideoLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const startSelection = () => {
    setIsSelecting(true);
    setSelectionStart(null);
    setSelectionEnd(null);
  };

  const clearSelection = () => {
    setSelectionStart(null);
    setSelectionEnd(null);
    setIsSelecting(false);
  };

  const downloadSegment = () => {
    if (selectionStart !== null && selectionEnd !== null && selectedRecordingId) {
      const startTime = Math.min(selectionStart, selectionEnd);
      const endTime = Math.max(selectionStart, selectionEnd);
      
      const downloadUrl = `/api/archive/download/${selectedRecordingId.streamId}/${selectedRecordingId.filename}?start=${startTime}&end=${endTime}&format=${downloadFormat}`;
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${selectedRecordingId.streamId}_${formatTime(startTime)}-${formatTime(endTime)}.${downloadFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const jumpToTime = (time) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    clearError();
    if (selectedStreamId) {
      await loadRecordings(selectedStreamId);
    } else {
      await loadAvailableStreams();
    }
  };

  // Handle restart recordings
  const handleRestartRecordings = async () => {
    try {
      await restartRecordings();
      await handleRefresh();
    } catch (error) {
      console.error('Failed to restart recordings:', error);
    }
  };

  const handlePlayRecording = (recording) => {
    if (recording && recording.stream_id && recording.filename) {
      const streamUrl = archiveApi.getRecordingStreamUrl(recording.stream_id, recording.filename);
      
      const recordingData = {
        id: recording.filename,
        streamId: recording.stream_id,
        filename: recording.filename,
        streamUrl: streamUrl,
        timestamp: recording.timestamp,
        size: recording.size_mb
      };

      onSelectRecording(recordingData);
    }
  };

  // Enhanced UI Components
  const RecordingStatusIndicator = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-black">Recording Status</h3>
        <div className="flex items-center text-sm text-gray-600">
          <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
          System Active
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {cameras.slice(0, 3).map((camera) => (
          <div key={camera.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <Video className="w-4 h-4 mr-2 text-gray-600" />
              <span className="text-sm font-medium text-black">
                {camera.collection || 'Camera'} ({camera.ip})
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-xs text-gray-600">Active</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const FilterBar = () => (
    <div className="filter-bar">
      <div className="filter-controls">
        <div className="filter-group">
          <Calendar className="filter-icon" />
          <select
            value={filterDate}
            onChange={(e) => setFilterDate(e.target.value)}
            className="filter-select"
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="all">All Time</option>
          </select>
        </div>

        <div className="filter-group">
          <Filter className="filter-icon" />
          <select
            value={filterCamera}
            onChange={(e) => setFilterCamera(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Cameras</option>
            {cameras.map(camera => (
              <option key={camera.id} value={camera.id}>
                {camera.collection || 'Camera'} ({camera.ip})
              </option>
            ))}
          </select>
        </div>

        <div className="search-group">
          <Search className="search-icon" />
          <input
            type="text"
            placeholder="Search recordings..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="view-mode-group">
          <button
            onClick={() => setViewMode('grid')}
            className={`view-mode-button ${viewMode === 'grid' ? 'active' : ''}`}
          >
            <Grid className="view-icon" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`view-mode-button ${viewMode === 'list' ? 'active' : ''}`}
          >
            <List className="view-icon" />
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-50">
      <div className="flex-1 overflow-auto">
        {selectedRecordingId ? (
          // Enhanced video player view
          <div className="p-4 lg:p-6">
            <div className="flex items-center mb-4">
              <button
                onClick={() => onSelectRecording(null)}
                className="flex items-center text-gray-600 hover:text-black mr-4"
              >
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Recordings
              </button>
            </div>
            {/* Enhanced Video Player */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4">
              <div className="bg-gray-900 relative">
                <video
                  ref={videoRef}
                  className="w-full h-64 md:h-96 object-cover"
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onTimeUpdate={handleVideoTimeUpdate}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                  src={selectedRecordingId.streamUrl}
                >
                  <source src={selectedRecordingId.streamUrl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={handlePlayPause}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-4 rounded-full transition-all"
                  >
                    {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                  </button>
                </div>
              </div>

              {/* Video Controls */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={handlePlayPause}
                      className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      {isPlaying ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
                      {isPlaying ? 'Pause' : 'Play'}
                    </button>
                    <span className="text-sm text-gray-600">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => jumpToTime(0)}
                      className="p-2 hover:bg-gray-100 rounded-lg"
                      title="Go to start"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => jumpToTime(currentTime - 10)}
                      className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                      title="Back 10s"
                    >
                      -10s
                    </button>
                    <button
                      onClick={() => jumpToTime(currentTime + 10)}
                      className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                      title="Forward 10s"
                    >
                      +10s
                    </button>
                  </div>
                </div>

                {/* Timeline */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium text-black">Timeline</label>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={startSelection}
                        className={`px-3 py-1 text-sm rounded-lg ${
                          isSelecting ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 hover:bg-gray-200'
                        }`}
                      >
                        {isSelecting ? 'Selecting...' : 'Select Range'}
                      </button>
                      {(selectionStart !== null || selectionEnd !== null) && (
                        <button
                          onClick={clearSelection}
                          className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                        >
                          Clear
                        </button>
                      )}
                    </div>
                  </div>

                  <div
                    ref={timelineRef}
                    className="relative w-full h-8 bg-gray-200 rounded-lg cursor-pointer"
                    onClick={handleTimelineClick}
                  >
                    {/* Progress bar */}
                    <div
                      className="absolute top-0 left-0 h-full bg-gray-400 rounded-lg"
                      style={{ width: `${(currentTime / duration) * 100}%` }}
                    />

                    {/* Selection overlay */}
                    {selectionStart !== null && selectionEnd !== null && (
                      <div
                        className="absolute top-0 h-full bg-blue-300 bg-opacity-50 rounded-lg"
                        style={{
                          left: `${(Math.min(selectionStart, selectionEnd) / duration) * 100}%`,
                          width: `${(Math.abs(selectionEnd - selectionStart) / duration) * 100}%`
                        }}
                      />
                    )}

                    {/* Current time indicator */}
                    <div
                      className="absolute top-0 w-1 h-full bg-red-500 rounded-full"
                      style={{ left: `${(currentTime / duration) * 100}%` }}
                    />

                    {/* Selection markers */}
                    {selectionStart !== null && (
                      <div
                        className="absolute top-0 w-2 h-full bg-blue-500 rounded-full"
                        style={{ left: `${(selectionStart / duration) * 100}%` }}
                      />
                    )}
                    {selectionEnd !== null && (
                      <div
                        className="absolute top-0 w-2 h-full bg-blue-500 rounded-full"
                        style={{ left: `${(selectionEnd / duration) * 100}%` }}
                      />
                    )}
                  </div>

                  {/* Time markers */}
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>00:00:00</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                </div>

                {/* Selection Info */}
                {selectionStart !== null && selectionEnd !== null && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-blue-800">
                        <div className="font-medium">Selected Range:</div>
                        <div>
                          {formatTime(Math.min(selectionStart, selectionEnd))} - {formatTime(Math.max(selectionStart, selectionEnd))}
                        </div>
                        <div className="text-xs">
                          Duration: {formatTime(Math.abs(selectionEnd - selectionStart))}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <select
                          value={downloadFormat}
                          onChange={(e) => setDownloadFormat(e.target.value)}
                          className="text-sm border border-blue-300 rounded-lg px-2 py-1"
                        >
                          <option value="mp4">MP4</option>
                          <option value="avi">AVI</option>
                          <option value="mov">MOV</option>
                        </select>
                        <button
                          onClick={downloadSegment}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          Download Segment
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Quick Time Jumps */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="text-sm text-gray-600 mr-2">Quick Jump:</span>
                  {[0, 0.25, 0.5, 0.75, 1].map((percentage) => (
                    <button
                      key={percentage}
                      onClick={() => jumpToTime(duration * percentage)}
                      className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      {percentage === 0 ? 'Start' : percentage === 1 ? 'End' : `${percentage * 100}%`}
                    </button>
                  ))}
                </div>
              </div>

              {/* Recording Info */}
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-black">
                    {archiveApi.parseStreamId(selectedRecordingId.streamId).collectionName}
                  </h3>
                  <span className="text-sm text-gray-600">
                    {archiveApi.parseStreamId(selectedRecordingId.streamId).cameraIp}
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatDate(selectedRecordingId.timestamp)}
                  </div>
                  <div className="flex items-center">
                    <Video className="w-4 h-4 mr-1" />
                    {archiveApi.formatRecordingDuration(selectedRecordingId)}
                  </div>
                  <div className="flex items-center">
                    <HardDrive className="w-4 h-4 mr-1" />
                    {archiveApi.formatFileSize(selectedRecordingId.size * 1024 * 1024)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Enhanced recordings list view
          <div className="p-4 lg:p-6">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-black">Archive Playback</h1>
            </div>

            <RecordingStatusIndicator />
            <FilterBar />
            
            {/* Loading state */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading recordings...</p>
                </div>
              </div>
            )}

            {/* Error state */}
            {error && (
              <div className="bg-white border border-red-200 rounded-lg p-6 mb-4">
                <div className="flex items-center mb-4">
                  <div className="text-red-500 mr-3">⚠️</div>
                  <h3 className="text-lg font-semibold text-red-800">Error Loading Recordings</h3>
                </div>
                <p className="text-red-700 mb-4">{error}</p>
                {error.includes('backend is not available') && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-800 font-medium mb-2">
                      The archive backend is currently not running. To access recordings:
                    </p>
                    <ul className="text-red-700 text-sm space-y-1 ml-4">
                      <li>• Start the Python backend server</li>
                      <li>• Ensure the archive recording service is running</li>
                      <li>• Check that recordings exist in the ./recordings/ directory</li>
                    </ul>
                  </div>
                )}
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            )}

            {/* Recordings grid */}
            {!isLoading && !error && (
              <div className={`grid gap-4 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {filteredRecordings.length === 0 ? (
                  <div className="col-span-full bg-white border border-gray-200 rounded-lg p-12 text-center">
                    <div className="text-6xl mb-4">📁</div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No archived recordings found</h3>
                    <p className="text-gray-600 mb-4">
                      {selectedStreamId
                        ? "No recordings available for the selected camera."
                        : "Select a camera to view its recordings."
                      }
                    </p>
                    {selectedStreamId && (
                      <button
                        onClick={() => setFilters({ dateRange: 'all', sortBy: 'newest' })}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Show All Recordings
                      </button>
                    )}
                  </div>
                ) : (
                  filteredRecordings.map(recording => (
                    <div
                      key={recording.filename}
                      className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => handlePlayRecording(recording)}
                    >
                      <div className="relative">
                        <div className="w-full h-32 bg-gray-200 flex items-center justify-center">
                          <Video className="w-8 h-8 text-gray-400" />
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          {archiveApi.formatRecordingDuration(recording)}
                        </div>
                      </div>
                      <div className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-black text-sm">
                            {archiveApi.parseStreamId(recording.stream_id).collectionName}
                          </h4>
                          <span className="text-xs text-gray-600">
                            {archiveApi.formatFileSize(recording.size_bytes)}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">
                          {formatDate(recording.timestamp)}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {archiveApi.parseStreamId(recording.stream_id).cameraIp}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(EnhancedArchivePlayback);

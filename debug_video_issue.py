#!/usr/bin/env python3
"""
Debug script to identify the video playback issue
"""

import requests
import json
from pathlib import Path

def test_video_streaming():
    """Test the video streaming functionality"""
    print("=== Video Streaming Debug ===\n")
    
    # Test 1: Get available streams
    try:
        response = requests.get('http://localhost:8000/api/archive/streams', timeout=5)
        if response.status_code == 200:
            streams = response.json()
            print(f"✓ Found {len(streams.get('streams', []))} streams")
            
            if streams.get('streams'):
                stream_id = streams['streams'][0]
                print(f"Testing stream: {stream_id}")
                
                # Test 2: Get recordings for the stream
                recordings_response = requests.get(f'http://localhost:8000/api/archive/list/{stream_id}', timeout=5)
                if recordings_response.status_code == 200:
                    recordings = recordings_response.json()
                    if recordings.get('recordings'):
                        recording = recordings['recordings'][0]
                        filename = recording['filename']
                        print(f"Testing recording: {filename}")
                        
                        # Test 3: Check if file exists locally
                        local_path = Path(f'./recordings/{stream_id}/{filename}')
                        if local_path.exists():
                            file_size = local_path.stat().st_size
                            print(f"✓ Local file exists: {file_size} bytes ({file_size/1024/1024:.1f} MB)")
                            
                            # Check file signature
                            with open(local_path, 'rb') as f:
                                first_bytes = f.read(32)
                                print(f"File signature: {first_bytes[:8].hex()}")
                                if b'ftyp' in first_bytes[:20]:
                                    print("✓ Valid MP4 signature found")
                                else:
                                    print("✗ Invalid MP4 signature")
                        else:
                            print(f"✗ Local file not found: {local_path}")
                        
                        # Test 4: Test streaming URL
                        stream_url = f'http://localhost:8000/api/archive/stream/{stream_id}/{filename}'
                        print(f"Testing URL: {stream_url}")
                        
                        try:
                            # Test with range request
                            range_response = requests.get(
                                stream_url, 
                                headers={'Range': 'bytes=0-1023'}, 
                                timeout=10
                            )
                            print(f"Range request status: {range_response.status_code}")
                            if range_response.status_code in [200, 206]:
                                print(f"Content-Type: {range_response.headers.get('content-type')}")
                                print(f"Content-Length: {range_response.headers.get('content-length')}")
                                print(f"Accept-Ranges: {range_response.headers.get('accept-ranges')}")
                                print(f"Received {len(range_response.content)} bytes")
                                
                                # Check if content looks like MP4
                                content = range_response.content
                                if b'ftyp' in content[:20]:
                                    print("✓ Streaming content has valid MP4 signature")
                                else:
                                    print("✗ Streaming content does not have valid MP4 signature")
                                    print(f"First 32 bytes: {content[:32].hex()}")
                            else:
                                print(f"✗ Range request failed: {range_response.text}")
                                
                        except Exception as e:
                            print(f"✗ Streaming test failed: {e}")
                        
                        # Test 5: Test full GET request
                        try:
                            full_response = requests.get(stream_url, stream=True, timeout=5)
                            print(f"Full GET status: {full_response.status_code}")
                            if full_response.status_code == 200:
                                # Read first chunk
                                chunk = next(full_response.iter_content(1024))
                                print(f"First chunk size: {len(chunk)} bytes")
                                if b'ftyp' in chunk[:20]:
                                    print("✓ Full streaming has valid MP4 signature")
                                else:
                                    print("✗ Full streaming does not have valid MP4 signature")
                            else:
                                print(f"✗ Full GET failed: {full_response.text}")
                        except Exception as e:
                            print(f"✗ Full GET test failed: {e}")
                            
                    else:
                        print("✗ No recordings found")
                else:
                    print(f"✗ Failed to get recordings: {recordings_response.status_code}")
            else:
                print("✗ No streams available")
        else:
            print(f"✗ Failed to get streams: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error: {e}")

def check_ffmpeg_settings():
    """Check if FFmpeg is creating compatible files"""
    print("\n=== FFmpeg Compatibility Check ===\n")
    
    recordings_dir = Path('./recordings')
    if recordings_dir.exists():
        for stream_dir in recordings_dir.iterdir():
            if stream_dir.is_dir():
                mp4_files = list(stream_dir.glob('*.mp4'))
                if mp4_files:
                    # Check the newest file
                    newest_file = max(mp4_files, key=lambda f: f.stat().st_mtime)
                    print(f"Checking: {newest_file}")
                    
                    # Basic file info
                    stat = newest_file.stat()
                    print(f"Size: {stat.st_size} bytes ({stat.st_size/1024/1024:.1f} MB)")
                    
                    # Check file signature
                    with open(newest_file, 'rb') as f:
                        first_bytes = f.read(32)
                        print(f"Signature: {first_bytes[:8].hex()}")
                        
                        if b'ftyp' in first_bytes[:20]:
                            print("✓ Valid MP4 container")
                            
                            # Look for codec info
                            f.seek(0)
                            first_kb = f.read(1024)
                            if b'avc1' in first_kb or b'h264' in first_kb:
                                print("✓ H.264 codec detected")
                            else:
                                print("? H.264 codec not clearly detected in first KB")
                        else:
                            print("✗ Invalid MP4 container")
                    break

if __name__ == "__main__":
    test_video_streaming()
    check_ffmpeg_settings()

/* Current Recordings Component Styles */

.current-recordings-content {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.current-recordings-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.current-recordings-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-title h1 {
  font-size: 28px;
  font-weight: 700;
  color: #132447;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-title p {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 36, 71, 0.3);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.refresh-icon {
  width: 16px;
  height: 16px;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Recording Status Card */
.recording-status-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #132447;
  margin: 0;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
}

.status-icon {
  width: 16px;
  height: 16px;
}

.status-icon.active {
  color: #28a745;
}

.status-icon.error {
  color: #dc3545;
}

.status-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-item-icon {
  width: 20px;
  height: 20px;
  color: #00299d;
}

.status-item-content {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.status-value {
  font-size: 16px;
  color: #132447;
  font-weight: 600;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #00299d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-container p {
  color: #6c757d;
  font-size: 16px;
  margin: 0;
}

/* Error State */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  text-align: center;
}

.error-icon {
  width: 48px;
  height: 48px;
  color: #dc3545;
  margin-bottom: 16px;
}

.error-container h3 {
  color: #132447;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.error-container p {
  color: #6c757d;
  font-size: 16px;
  margin: 0 0 20px 0;
}

.retry-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 36, 71, 0.3);
}

/* Recordings Section */
.recordings-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #132447;
  margin: 0;
}

.recordings-count {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* No Recordings State */
.no-recordings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.no-recordings-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-recordings h3 {
  color: #132447;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.no-recordings p {
  color: #6c757d;
  font-size: 16px;
  margin: 0 0 8px 0;
}

.no-recordings .hint {
  font-size: 14px;
  color: #adb5bd;
}

/* Recordings Grid */
.recordings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

/* Recording Card */
.recording-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.recording-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #00299d;
}

/* Recording Thumbnail */
.recording-thumbnail {
  position: relative;
  width: 100%;
  height: 180px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  width: 48px;
  height: 48px;
  color: #adb5bd;
}

.recording-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

.recording-dot {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Recording Content */
.recording-content {
  padding: 16px;
}

.recording-header {
  margin-bottom: 12px;
}

.recording-title {
  font-size: 16px;
  font-weight: 600;
  color: #132447;
  margin: 0 0 4px 0;
}

.camera-ip {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.recording-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
}

.detail-icon {
  width: 16px;
  height: 16px;
  color: #00299d;
}

.recording-actions {
  display: flex;
  gap: 8px;
}

.view-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.view-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 36, 71, 0.3);
}

.button-icon {
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .current-recordings-content {
    padding: 16px;
  }
  
  .current-recordings-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: flex-end;
  }
  
  .status-info {
    grid-template-columns: 1fr;
  }
  
  .recordings-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}

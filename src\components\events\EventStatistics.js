import React, { useState, useEffect } from 'react';
import { fetchEventRules, fetchEventStatistics } from '../../services/eventsService';
import './EventStatistics.css';

const EventStatistics = () => {
  const [rules, setRules] = useState([]);
  const [statistics, setStatistics] = useState([]);
  const [selectedRules, setSelectedRules] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartType, setChartType] = useState('pie'); // 'pie' or 'bar'

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load rules
      const rulesResponse = await fetchEventRules();
      if (!rulesResponse.success) {
        throw new Error(rulesResponse.error || 'Failed to load event rules');
      }
      
      // Load statistics
      const statsResponse = await fetchEventStatistics();
      if (!statsResponse.success) {
        throw new Error(statsResponse.error || 'Failed to load event statistics');
      }
      
      setRules(rulesResponse.data.rules);
      setStatistics(statsResponse.data.statistics);
      
      // Default to selecting all enabled rules
      const enabledRuleIds = rulesResponse.data.rules
        .filter(rule => rule.enabled)
        .map(rule => rule.id);
      
      setSelectedRules(enabledRuleIds);
      
    } catch (err) {
      setError('Error loading data: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRuleToggle = (ruleId) => {
    setSelectedRules(prev => {
      if (prev.includes(ruleId)) {
        return prev.filter(id => id !== ruleId);
      } else {
        return [...prev, ruleId];
      }
    });
  };

  const handleCameraChange = (e) => {
    setSelectedCamera(e.target.value);
  };

  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  // Filter statistics based on selected rules and camera
  const filteredStatistics = statistics.filter(stat => {
    const matchesRule = selectedRules.includes(stat.event_id);
    const matchesCamera = !selectedCamera || stat.camera_id === selectedCamera;
    return matchesRule && matchesCamera;
  });

  // Get unique camera IDs from statistics
  const uniqueCameras = [...new Set(statistics.map(stat => stat.camera_id))];

  // Calculate total counts for pie chart
  const eventCounts = {};
  filteredStatistics.forEach(stat => {
    if (!eventCounts[stat.event_name]) {
      eventCounts[stat.event_name] = 0;
    }
    eventCounts[stat.event_name] += stat.count;
  });

  // Generate colors for pie chart
  const generateColor = (index) => {
    const colors = [
      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
      '#FF9F40', '#8AC249', '#EA5545', '#F46A9B', '#EF9B20',
      '#EDBF33', '#87BC45', '#27AEEF', '#B33DC6'
    ];
    return colors[index % colors.length];
  };

  if (loading && rules.length === 0) {
    return <div className="event-statistics-loading">Loading event statistics...</div>;
  }

  if (error && rules.length === 0) {
    return <div className="event-statistics-error">{error}</div>;
  }

  return (
    <div className="event-statistics">
      <div className="event-statistics-header">
        <h2>Event Statistics</h2>
        <div className="chart-type-selector">
          <button 
            className={`chart-type-btn ${chartType === 'pie' ? 'active' : ''}`}
            onClick={() => handleChartTypeChange('pie')}
          >
            Pie Chart
          </button>
          <button 
            className={`chart-type-btn ${chartType === 'bar' ? 'active' : ''}`}
            onClick={() => handleChartTypeChange('bar')}
          >
            Bar Chart
          </button>
        </div>
      </div>
      
      <div className="event-statistics-filters">
        <div className="rules-filter">
          <h3>Rules</h3>
          <div className="rules-list">
            {rules.map(rule => (
              <div key={rule.id} className="rule-checkbox">
                <input 
                  type="checkbox" 
                  id={`rule-${rule.id}`}
                  checked={selectedRules.includes(rule.id)} 
                  onChange={() => handleRuleToggle(rule.id)}
                />
                <label htmlFor={`rule-${rule.id}`}>{rule.name}</label>
              </div>
            ))}
          </div>
        </div>
        
        <div className="camera-filter">
          <h3>Camera</h3>
          <select value={selectedCamera} onChange={handleCameraChange}>
            <option value="">All Cameras</option>
            {uniqueCameras.map(camera => (
              <option key={camera} value={camera}>{camera}</option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="event-statistics-visualization">
        {filteredStatistics.length === 0 ? (
          <div className="no-data-message">No data available for the selected filters</div>
        ) : (
          <div className="chart-container">
            {chartType === 'pie' ? (
              <div className="pie-chart">
                <svg viewBox="0 0 100 100">
                  {/* Simplified pie chart visualization */}
                  <circle cx="50" cy="50" r="40" fill="#2c2c2c" />
                  
                  {/* This is a simplified representation - in a real app, you would use a charting library */}
                  {Object.keys(eventCounts).map((eventName, index) => {
                    const total = Object.values(eventCounts).reduce((sum, count) => sum + count, 0);
                    const percentage = (eventCounts[eventName] / total) * 100;
                    return (
                      <circle 
                        key={index}
                        cx="50" 
                        cy="50" 
                        r={40 * (percentage / 100)} 
                        fill={generateColor(index)}
                      />
                    );
                  })}
                </svg>
                
                <div className="chart-legend">
                  {Object.keys(eventCounts).map((eventName, index) => (
                    <div key={index} className="legend-item">
                      <div className="legend-color" style={{ backgroundColor: generateColor(index) }}></div>
                      <div className="legend-label">{eventName}: {eventCounts[eventName]}</div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="bar-chart">
                {/* Simplified bar chart visualization */}
                <div className="bars-container">
                  {Object.keys(eventCounts).map((eventName, index) => {
                    const maxCount = Math.max(...Object.values(eventCounts));
                    const percentage = (eventCounts[eventName] / maxCount) * 100;
                    return (
                      <div key={index} className="bar-item">
                        <div className="bar-label">{eventName}</div>
                        <div className="bar-container">
                          <div 
                            className="bar" 
                            style={{ 
                              width: `${percentage}%`,
                              backgroundColor: generateColor(index)
                            }}
                          ></div>
                          <span className="bar-value">{eventCounts[eventName]}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EventStatistics;

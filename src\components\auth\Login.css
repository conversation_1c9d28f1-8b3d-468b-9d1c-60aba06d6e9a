.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #1E0B38;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1E0B38 0%, #3A1670 50%, #4A1D94 100%);
  z-index: 0;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 229, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 229, 255, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.15;
}

.login-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 40%, rgba(0, 229, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%),
    radial-gradient(circle at 70% 60%, rgba(122, 50, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
}

.login-card {
  position: relative;
  z-index: 1;
  background-color: rgba(30, 11, 56, 0.7);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5), 0 0 20px rgba(0, 229, 255, 0.2);
  width: 100%;
  max-width: 400px;
  padding: 35px;
  color: #fff;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 229, 255, 0.1);
  overflow: hidden;
}

.login-header {
  text-align: center;
  margin-bottom: 35px;
  position: relative;
}

.login-header::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #00E5FF, #7A32FF);
  border-radius: 3px;
}

.login-logo {
  width: 100px;
  height: auto;
  margin-bottom: 15px;
  filter: drop-shadow(0 0 15px rgba(0, 229, 255, 0.5));
  border-radius: 50%;
  padding: 5px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 229, 255, 0.2);
}

.login-header h2 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(to right, #FFFFFF, #00E5FF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 229, 255, 0.3);
}

.login-subtitle {
  font-size: 14px;
  color: rgba(0, 229, 255, 0.8);
  margin-top: 5px;
  letter-spacing: 1.5px;
  font-weight: 500;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 22px;
  position: relative;
  z-index: 1;
}

.login-error {
  background-color: rgba(255, 61, 129, 0.1);
  border-left: 4px solid rgba(255, 61, 129, 0.8);
  padding: 12px 15px;
  color: rgba(255, 61, 129, 1);
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.input-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 15px;
  color: rgba(0, 229, 255, 0.7);
  font-size: 18px;
  transition: all 0.3s ease;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group select {
  padding: 16px 15px 16px 45px;
  border-radius: 12px;
  border: 1px solid rgba(0, 229, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 15px;
  transition: all 0.3s;
  width: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
  font-family: 'Montserrat', sans-serif;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus,
.form-group select:focus {
  border-color: rgba(0, 229, 255, 0.6);
  outline: none;
  box-shadow: 0 0 15px rgba(0, 229, 255, 0.3);
  background-color: rgba(0, 0, 0, 0.3);
}

.form-group input[type="text"]:focus + .input-icon,
.form-group input[type="password"]:focus + .input-icon,
.form-group select:focus + .input-icon {
  color: rgba(0, 229, 255, 1);
  text-shadow: 0 0 8px rgba(0, 229, 255, 0.5);
}

.role-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px;
  padding-right: 40px;
  cursor: pointer;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 229, 255, 0.2);
  color: rgba(0, 229, 255, 0.7);
  cursor: pointer;
  font-size: 12px;
  padding: 5px 10px;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.toggle-password:hover {
  color: rgba(0, 229, 255, 1);
  background: rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 10px rgba(0, 229, 255, 0.2);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -5px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.remember-me input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #00E5FF;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 229, 255, 0.3);
}

.remember-me label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  letter-spacing: 0.3px;
}

.login-button {
  padding: 16px;
  background: linear-gradient(135deg, #00E5FF, #7A32FF);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 15px;
  box-shadow: 0 4px 15px rgba(0, 229, 255, 0.3);
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s;
}

.login-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 229, 255, 0.4);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 229, 255, 0.3);
}

.login-button:disabled {
  background: linear-gradient(135deg, #666, #888);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.login-footer {
  margin-top: 35px;
  text-align: center;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  border-top: 1px solid rgba(0, 229, 255, 0.1);
  padding-top: 25px;
  position: relative;
}

.login-footer::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    rgba(0, 229, 255, 0),
    rgba(0, 229, 255, 0.3),
    rgba(0, 229, 255, 0));
}

.login-note {
  background-color: rgba(0, 229, 255, 0.05);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  text-align: left;
  border: 1px solid rgba(0, 229, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.login-note p {
  margin: 5px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.login-note strong {
  color: rgba(0, 229, 255, 0.9);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.credential {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
  font-family: monospace;
  color: #00E5FF;
  font-weight: 500;
  border: 1px solid rgba(0, 229, 255, 0.2);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.login-info {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.6;
  margin-top: 15px;
  letter-spacing: 0.3px;
}

@media (max-width: 500px) {
  .login-card {
    padding: 25px 20px;
    max-width: 90%;
  }

  .login-logo {
    width: 80px;
  }

  .login-header h2 {
    font-size: 24px;
  }

  .login-subtitle {
    font-size: 12px;
  }

  .form-group input[type="text"],
  .form-group input[type="password"],
  .form-group select {
    padding: 14px 15px 14px 40px;
    font-size: 14px;
  }

  .input-icon {
    font-size: 16px;
    left: 12px;
  }

  .toggle-password {
    padding: 4px 8px;
    font-size: 11px;
  }

  .login-button {
    padding: 14px;
    font-size: 15px;
  }
}

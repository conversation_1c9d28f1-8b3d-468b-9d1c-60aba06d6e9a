.sender-config-container {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #ffffff;
}

.sender-config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #333333;
}

.sender-config-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  padding-top: 10px;
}

.alert-type-section,
.settings-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid #333333;
}

.alert-type-section h3,
.settings-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #E37814;
  border-bottom: 1px solid #333333;
  padding-bottom: 12px;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.settings-content {
  margin-top: 16px;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
  max-height: 2000px;
  opacity: 1;
}

.settings-content.collapsed {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  pointer-events: none;
}

.toggle-icon {
  font-size: 16px;
  color: #E37814;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.alert-type-options {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.alert-type-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.alert-type-option input[type="radio"] {
  margin-right: 8px;
  cursor: pointer;
  accent-color: #E37814;
  width: 18px;
  height: 18px;
}

.radio-label {
  font-size: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #cccccc;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #333333;
  border-radius: 4px;
  background-color: #262626;
  color: #ffffff;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #E37814;
  outline: none;
}

.form-group input::placeholder {
  color: #666666;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.save-button {
  padding: 10px 24px;
  background-color: #E37814;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.save-button:hover {
  background-color: #f48a2c;
  transform: translateY(-1px);
}

.save-button:active {
  transform: translateY(1px);
}

.save-button:disabled {
  background-color: #7d4b20;
  cursor: not-allowed;
  transform: none;
}

.test-sms-action {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
}

.test-button {
  padding: 8px 16px;
  background-color: #2c7fb8;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.test-button:hover {
  background-color: #3a8bc2;
  transform: translateY(-1px);
}

.test-button:active {
  transform: translateY(1px);
}

.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #333333;
  border-radius: 4px;
  background-color: #262626;
  color: #ffffff;
  font-size: 14px;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-group textarea:focus {
  border-color: #E37814;
  outline: none;
}

.form-group textarea::placeholder {
  color: #666666;
}

.error-message {
  padding: 12px 16px;
  background-color: rgba(255, 99, 71, 0.2);
  border-left: 4px solid #ff6347;
  color: #ff6347;
  margin-bottom: 16px;
  border-radius: 4px;
}

.success-message {
  padding: 12px 16px;
  background-color: rgba(75, 181, 67, 0.2);
  border-left: 4px solid #4bb543;
  color: #4bb543;
  margin-bottom: 16px;
  border-radius: 4px;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #cccccc;
}

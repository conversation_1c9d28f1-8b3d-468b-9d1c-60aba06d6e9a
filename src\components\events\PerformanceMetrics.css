.performance-metrics {
  display: flex;
  flex-direction: column;
  background-color: transparent;
  padding: 6px 12px;
  border-radius: 4px;
  gap: 2px;
  border: none;
  height: auto;
  font-size: 12px;
  order: 2; /* Ensure performance metrics come after user controls */
  /* Static display element - no interaction */
  cursor: default;
  pointer-events: none;
}

.metric-item {
  display: flex;
  align-items: center;
  padding: 2px 0;
  color: #333;
}

.metric-text {
  color: #333;
  font-weight: bold;
  font-size: 12px;
  font-family: monospace;
}

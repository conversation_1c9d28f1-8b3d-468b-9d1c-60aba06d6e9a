/*
  Archive Playback Content - Modern UI

  Breakpoints:
  - Mobile: < 768px (1 column)
  - Tablet: 768px - 1024px (2 columns)
  - Desktop: > 1024px (3+ columns)
*/

.archive-playback-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #ffffff;
  color: #000000;
  overflow: auto;
}

.recordings-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  color: #000000;
}

/* Archive Filter Container Styles */
.archive-filters-container {
  padding: 1rem 1.5rem;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.archive-filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e0e0e0;
}

.archive-title {
  display: flex;
  align-items: center;
}

.archive-title h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #000000;
  text-shadow: none;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: #000000;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.refresh-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background: #333333;
}

.refresh-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.3);
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.refresh-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.refresh-button:hover:not(:disabled) .refresh-icon {
  transform: rotate(180deg);
}

/* Restart button styling */
.restart-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #ff6b35, #ff4444);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.restart-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
  background: linear-gradient(135deg, #ff5722, #ff3333);
}

.restart-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.3);
}

.restart-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: linear-gradient(135deg, #666666, #555555);
}

.restart-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.restart-button:hover:not(:disabled) .restart-icon {
  transform: rotate(360deg);
}

.archive-filters-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.75rem;
  margin-top: 0.5rem;
  width: 100%;
}

/* Recording Cards Grid Layout with responsive breakpoints */
.recordings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  grid-gap: 24px;
  padding: 24px;
  overflow-y: auto;
}

/* Responsive grid adjustments */
@media (max-width: 767px) {
  .recordings-grid {
    grid-template-columns: 1fr; /* Single column for mobile */
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .recordings-grid {
    grid-template-columns: repeat(2, 1fr); /* Two columns for tablet */
  }
}

@media (min-width: 1024px) {
  .recordings-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)); /* Three or more columns for desktop */
  }
}

/* Card Design */
.recording-card {
  background-color: #1F1F1F;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  transition: transform 0.2s, box-shadow 0.2s;
}

.recording-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
}

/* Thumbnail Area with 16:9 aspect ratio */
.recording-thumbnail {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  background-color: #333;
  overflow: hidden;
}

.thumbnail-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Play overlay on hover */
.recording-thumbnail::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recording-thumbnail::before {
  content: '▶';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  color: white;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.2s;
}

.recording-card:hover .recording-thumbnail::after,
.recording-card:hover .recording-thumbnail::before {
  opacity: 1;
}

.camera-icon {
  font-size: 2.5rem;
  opacity: 0.5;
}

/* Info Section */
.recording-info {
  padding: 16px;
  flex: 1;
}

.recording-camera {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: white;
}

.recording-time {
  font-size: 12px;
  color: #CCCCCC;
  margin-bottom: 5px;
}

.recording-duration {
  font-size: 12px;
  color: #00E5FF; /* Using ravia-cyan for duration to highlight it */
  margin-bottom: 5px;
  font-weight: 500;
}

.recording-ip {
  font-size: 12px;
  color: #888888;
}

/* Action Buttons */
.recording-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  background-color: #1A1A1A;
}

.play-button, .delete-button {
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s, transform 0.1s;
  height: 32px;
}

.play-button {
  background-color: #3CB043;
  padding: 0 12px;
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.play-button svg {
  margin-right: 6px;
}

.play-button:hover {
  background-color: #34993B; /* 10% darker */
}

.play-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3CB043;
  outline-offset: 2px;
}

.delete-button {
  background-color: #C0392B;
  width: 32px;
  padding: 0;
}

.delete-button:hover {
  background-color: #A93226; /* 10% darker */
}

.delete-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #C0392B;
  outline-offset: 2px;
}

/* Empty State Styling */
.no-recordings {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #CCCCCC;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-recordings-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #555;
}

.no-recordings p {
  margin: 8px 0;
  font-size: 16px;
}

.hint {
  font-size: 14px;
  color: #888888;
  margin-bottom: 24px;
}

.clear-filter-button {
  background-color: #444;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-filter-button:hover {
  background-color: #555;
}

.clear-filter-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #777;
}

/* Archive Filter Responsive Design */
@media (max-width: 1200px) {
  .archive-filters-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.625rem;
  }
}

@media (max-width: 900px) {
  .archive-filters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .archive-filters-container {
    padding: 1rem;
  }

  .archive-filters-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .archive-title h2 {
    font-size: 1.25rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .refresh-button,
  .restart-button {
    align-self: stretch;
    justify-content: center;
  }

  .archive-filters-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .archive-filters-container {
    padding: 0.75rem;
  }

  .archive-title h2 {
    font-size: 1.125rem;
  }

  .refresh-button,
  .restart-button {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }
}

@media (min-width: 1400px) {
  .archive-filters-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .refresh-button,
  .restart-button,
  .refresh-icon,
  .restart-icon {
    transition: none;
  }

  .refresh-button:hover .refresh-icon,
  .restart-button:hover .restart-icon {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .archive-filters-container {
    border-bottom-width: 2px;
  }

  .archive-filters-header {
    border-bottom-width: 2px;
  }

  .refresh-button {
    border: 2px solid #00E5FF;
  }

  .restart-button {
    border: 2px solid #ff6b35;
  }
}

/* Video Player Styles */
.recording-player-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, #1E0B38, #2A0F4C);
}

.player-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(122, 50, 255, 0.3);
}

.back-button {
  background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
  border: 1px solid rgba(122, 50, 255, 0.3);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.back-button:hover {
  background: linear-gradient(135deg, #3d3d3d, #4d4d4d);
  border-color: rgba(122, 50, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.recording-details h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(182, 225, 75, 0.3);
}

.recording-timestamp,
.recording-size,
.recording-duration {
  margin: 2px 0;
  font-size: 14px;
  color: #cccccc;
}

.recording-duration {
  color: #00E5FF; /* Using ravia-cyan for duration to highlight it */
  font-weight: 500;
}

.video-player-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.archive-video-player {
  width: 100%;
  height: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.video-error {
  text-align: center;
  color: #ff6b6b;
  padding: 40px;
}

.video-error p {
  font-size: 16px;
  margin: 0;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  grid-column: 1 / -1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(122, 50, 255, 0.3);
  border-top: 4px solid #7A32FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #cccccc;
  font-size: 16px;
  margin: 0;
}

.error-container {
  color: #ff6b6b;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-message {
  font-size: 16px;
  margin: 0 0 20px 0;
  color: #ff6b6b;
}

.retry-button {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.retry-button:hover {
  background: linear-gradient(135deg, #ff5252, #f44336);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

/* Recording size display */
.recording-size {
  font-size: 12px;
  color: #888888;
  margin-top: 4px;
}

/* Backend unavailable info styling */
.backend-unavailable-info {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 153, 0, 0.1);
  border: 1px solid rgba(255, 153, 0, 0.3);
  border-radius: 0.5rem;
  text-align: left;
}

.backend-unavailable-info .info-text {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-weight: 500;
}

.backend-unavailable-info .info-list {
  margin: 0;
  padding-left: 1.5rem;
  color: #cccccc;
}

.backend-unavailable-info .info-list li {
  margin-bottom: 0.25rem;
}

/* Camera name with recording status */
.camera-name-with-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.camera-name {
  flex: 1;
  min-width: 0; /* Allow text to truncate */
}

/* Archive title with status */
.archive-title {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.archive-title h2 {
  margin: 0;
}

/* Responsive adjustments for recording status */
@media (max-width: 768px) {
  .camera-name-with-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .archive-title {
    gap: 6px;
  }
}

/* Enhanced UI Components for Modern Archive Design */

/* Enhanced grid layout for recordings */
.recordings-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  padding: 20px 0;
}

/* Enhanced recording card with modern design */
.recording-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.recording-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: #132447;
}

/* Enhanced thumbnail styling */
.recording-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  color: rgba(255, 255, 255, 0.7);
}

.camera-icon {
  width: 32px;
  height: 32px;
}

/* Enhanced recording info section */
.recording-info {
  padding: 16px;
  background: #ffffff;
}

.recording-camera {
  font-size: 16px;
  font-weight: 600;
  color: #132447;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.recording-time {
  font-size: 13px;
  color: #6b7280;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.recording-duration {
  font-size: 12px;
  color: #00299d;
  margin: 0 0 4px 0;
  font-weight: 600;
}

.recording-size {
  font-size: 12px;
  color: #9ca3af;
  margin: 0 0 4px 0;
}

.recording-ip {
  font-size: 11px;
  color: #9ca3af;
  margin: 0;
  font-family: 'Courier New', monospace;
}

/* Enhanced action buttons */
.recording-actions {
  padding: 12px 16px;
  background: #f9fafb;
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.play-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(19, 36, 71, 0.2);
}

.play-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 36, 71, 0.3);
}

.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background: transparent;
  color: #ef4444;
  border: 1px solid #fecaca;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-button:hover {
  background: #fef2f2;
  border-color: #ef4444;
  transform: scale(1.05);
}

/* Enhanced loading and error states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: #ffffff;
  border-radius: 12px;
  margin: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: #ffffff;
  border: 1px solid #fecaca;
  border-radius: 12px;
  margin: 20px;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}

.error-icon {
  margin-bottom: 16px;
}

.error-message {
  color: #dc2626;
  font-weight: 500;
  margin-bottom: 16px;
  max-width: 600px;
  line-height: 1.6;
}

.retry-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #132447 0%, #00299d 100%);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 36, 71, 0.3);
}

/* Template-based Camera Settings Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #000000;
  height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin-bottom: 4px;
}

.header p {
  color: #666;
  font-size: 13px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;
  padding: 0 24px;
  flex-shrink: 0;
}

.tab {
  padding: 12px 24px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.tab.active {
  color: #000;
  border-bottom-color: #000;
  background-color: #ffffff;
}

.tab:hover:not(.active) {
  background-color: #f0f0f0;
  color: #333;
}

.settings-content {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  overflow-y: auto;
  flex: 1;
}

.settings-section {
  background-color: #ffffff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin-bottom: 16px;
  padding-bottom: 6px;
  border-bottom: 2px solid #f0f0f0;
}

.setting-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  min-height: 36px;
  padding: 4px 0;
}

.setting-label {
  font-size: 14px;
  color: #000;
  font-weight: 500;
  min-width: 120px;
  margin-right: 12px;
}

.setting-control {
  flex: 1;
  max-width: 240px;
}

select, input[type="number"], input[type="text"], input[type="password"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 13px;
  background-color: #ffffff;
  color: #000;
  transition: border-color 0.2s ease;
}

select:focus, input[type="number"]:focus, input[type="text"]:focus, input[type="password"]:focus {
  outline: none;
  border-color: #333;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

select:hover, input[type="number"]:hover, input[type="text"]:hover, input[type="password"]:hover {
  border-color: #ccc;
}

select:disabled, input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  border-color: #e9ecef;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-with-unit input, .input-with-unit select {
  flex: 1;
}

.unit-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  min-width: 50px;
}

.actions-section {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
  margin-top: 12px;
}

.button-group {
  display: flex;
  gap: 16px;
}

.save-button, .reset-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  min-width: 100px;
  justify-content: center;
}

.save-button {
  background-color: #000;
  color: #ffffff;
}

.save-button:hover:not(:disabled) {
  background-color: #333;
  transform: translateY(-1px);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reset-button {
  background-color: #ffffff;
  color: #000;
  border: 2px solid #e0e0e0;
}

.reset-button:hover:not(:disabled) {
  border-color: #ccc;
  background-color: #f9f9f9;
}

.reset-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-icon, .reset-icon {
  width: 14px;
  height: 14px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4CAF50;
}

.status-dot.error {
  background-color: #f44336;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .settings-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .tabs {
    flex-wrap: wrap;
  }

  .tab {
    padding: 12px 16px;
    font-size: 14px;
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .setting-control {
    max-width: 100%;
    width: 100%;
  }

  .settings-container {
    margin: 20px;
  }

  .header {
    padding: 20px;
  }

  .settings-content {
    padding: 20px;
  }
}

/* Legacy support for any remaining old components */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: black;
  font-weight: 500;
}

.credentials-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Legacy button styles for backward compatibility */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
}



.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #666;
}

/* Number input specific styles */
input[type="number"].form-control {
  text-align: center;
}

/* Responsive adjustments for stream settings */
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .current-settings-grid {
    grid-template-columns: 1fr;
  }
}

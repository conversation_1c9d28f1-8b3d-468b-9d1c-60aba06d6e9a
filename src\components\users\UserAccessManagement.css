.user-access-management {
  padding: 20px;
  color: #fff;
  background-color: #1a1a1a;
  height: 100%;
  overflow: auto;
}

.user-access-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-access-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.add-user-button {
  display: flex;
  align-items: center;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-user-button:hover {
  background-color: #45a049;
}

.add-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* Tab Navigation */
.user-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #444;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  color: #ccc;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #fff;
}

.tab-button.active {
  color: #f3f2f2;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #ffffff;
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 4px solid #ff3333;
  padding: 10px 15px;
  color: #ff6666;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Add User Form */
.add-user-form-container {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.add-user-form h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.form-error {
  color: #ff6666;
  margin-bottom: 15px;
  font-size: 14px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #ccc;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: #fff;
  font-size: 14px;
}

/* Username input with prefix styling */
.username-input-group {
  display: flex;
  align-items: center;
  background-color: #333;
  border: 1px solid #444;
  border-radius: 4px;
  overflow: hidden;
}

.username-prefix {
  padding: 10px;
  background-color: #444;
  color: #ccc;
  font-size: 14px;
  border-right: 1px solid #555;
  white-space: nowrap;
}

.username-with-prefix {
  border: none !important;
  border-radius: 0 !important;
  background-color: #333 !important;
  flex: 1;
}

.form-group input:focus {
  border-color: #4CAF50;
  outline: none;
}

/* Password requirements styling */
.password-requirements {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(255, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ff3333;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.password-requirements p {
  margin: 0 0 5px 0;
  font-size: 12px;
  color: #ff6666;
  font-weight: 600;
}

.password-requirements ul {
  margin: 0;
  padding-left: 20px;
  font-size: 12px;
  color: #ff9999;
}

.password-requirements li {
  margin-bottom: 2px;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.save-button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.save-button:hover {
  background-color: #45a049;
}

.save-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

.cancel-button {
  padding: 8px 16px;
  background-color: #666;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancel-button:hover {
  background-color: #555;
}

/* User Access Table */
.user-access-table-container {
  overflow-x: auto;
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 5px;
}

.user-access-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

.user-access-table th,
.user-access-table td {
  padding: 12px 15px;
  text-align: center;
  border-bottom: 1px solid #444;
}

.user-access-table th {
  background-color: #333;
  font-weight: 600;
  position: relative;
}

.service-column {
  text-align: left;
  width: 200px;
}

.user-column {
  position: relative;
  padding: 12px 15px;
}

.user-column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.username-header {
  font-weight: 600;
  color: #fff;
}

.role-badge-small {
  background-color: #666;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.role-badge-small.admin {
  background-color: #4CAF50;
  color: #000;
}

.role-badge-small.supervisor {
  background-color: #2196F3;
  color: #000;
}

.role-badge-small.superadmin {
  background-color: #E37814;
  color: #000;
}

/* Custom checkbox styling */
.permission-cell .checkbox-container {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
}

.permission-cell input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  cursor: pointer;
}

.permission-cell .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background-color: #333;
  border: 2px solid #555;
  border-radius: 4px;
  transition: all 0.1s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.permission-cell .checkmark:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0.1) 100%);
  opacity: 0.5;
  z-index: 0;
}

.permission-cell input[type="checkbox"]:checked ~ .checkmark {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.fixed-indicator {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  opacity: 0.5;
}

/* SuperAdmin fixed indicator */
.permission-cell .fixed-indicator {
  position: relative;
  top: auto;
  right: auto;
  transform: none;
  opacity: 0.8;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.permission-cell .fixed-indicator svg {
  stroke: #4CAF50;
}

.role-column.superadmin .fixed-indicator svg {
  stroke: #E37814;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
}

.modal-content h3 {
  margin-top: 0;
  color: #ff6666;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-button {
  padding: 8px 16px;
  background-color: #ff3333;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-button:hover {
  background-color: #e62e2e;
}

.confirm-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

/* User List Section */
.user-list-section {
  margin-top: 30px;
}

.user-list-section h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #fff;
  font-weight: 500;
}

.user-role-group {
  margin-bottom: 25px;
}

.user-role-group h4 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #ccc;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.user-role-group h4 .role-badge {
  margin-left: 10px;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.user-role-group h4 .role-badge.admin {
  background-color: #4CAF50;
  color: #000;
}

.user-role-group h4 .role-badge.supervisor {
  background-color: #2196F3;
  color: #000;
}

.user-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.user-card {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
}

.user-card.admin {
  border-left-color: #4CAF50;
}

.user-card.supervisor {
  border-left-color: #2196F3;
}

.user-card.superadmin {
  border-left-color: #E37814;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Newly created user animation */
@keyframes highlight-pulse {
  0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
  100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

.user-card.newly-created {
  animation: highlight-pulse 1.5s infinite;
  border-color: #4CAF50;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.new-user-badge {
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: #4CAF50;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.user-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.user-card-name {
  font-weight: 600;
  font-size: 16px;
  color: #fff;
}

.user-card-role {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.user-card-role.admin {
  background-color: #4CAF50;
  color: #000;
}

.user-card-role.supervisor {
  background-color: #2196F3;
  color: #000;
}

.user-card-role.superadmin {
  background-color: #E37814;
  color: #000;
}

.user-card-delete {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: #ff6666;
  font-size: 18px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 50%;
  line-height: 1;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.user-card-delete:hover {
  opacity: 1;
  background-color: rgba(255, 0, 0, 0.1);
}

.user-card-permissions {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.permission-tag {
  display: inline-block;
  background-color: #333;
  color: #999;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  opacity: 0.6;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.permission-tag:hover {
  opacity: 0.8;
  max-width: none;
}

.permission-tag.active {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
  opacity: 1;
}

.no-users-message {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  color: #aaa;
  text-align: center;
  font-style: italic;
}

/* Password display styling */
.password-detail {
  margin-top: 5px;
  border-top: 1px dashed #444;
  padding-top: 5px;
}

.password-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.password-value {
  font-family: monospace;
  letter-spacing: 1px;
  color: #ddd;
}

.password-toggle {
  background: none;
  border: none;
  color: #4CAF50;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.password-toggle:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: scale(1.1);
}

/* User Roles Table Styles */
.user-roles-table-container {
  margin-top: 20px;
}

.section-description {
  color: #aaa;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
}

.role-table-section {
  margin-bottom: 30px;
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.role-table-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 18px;
  color: #fff;
}

.role-badge {
  margin-left: 10px;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.role-badge.superadmin {
  background-color: #E37814;
  color: #000;
}

.role-badge.admin {
  background-color: #4CAF50;
  color: #000;
}

.role-badge.supervisor {
  background-color: #2196F3;
  color: #000;
}

.role-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 4px;
  overflow: hidden;
}

.role-table th,
.role-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #444;
}

.role-table th {
  background-color: #333;
  font-weight: 600;
  color: #ccc;
}

.role-table-row:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.role-table-row.superadmin-row {
  border-left: 4px solid #E37814;
}

.role-table-row.admin-row {
  border-left: 4px solid #4CAF50;
}

.role-table-row.supervisor-row {
  border-left: 4px solid #2196F3;
}

.role-table-row.newly-created {
  animation: highlight-pulse 1.5s infinite;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.new-badge {
  display: inline-block;
  background-color: #4CAF50;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.active {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-badge.inactive {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff6666;
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.permissions-cell {
  max-width: 300px;
}

.permissions-list-table {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  max-height: 80px;
  overflow-y: auto;
  padding: 5px 0;
}

.table-action-button {
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.table-action-button.delete {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff6666;
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.table-action-button.delete:hover {
  background-color: rgba(255, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-access-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .add-user-button {
    width: 100%;
    justify-content: center;
  }

  .user-cards {
    grid-template-columns: 1fr;
  }

  .role-table {
    display: block;
    overflow-x: auto;
  }
}

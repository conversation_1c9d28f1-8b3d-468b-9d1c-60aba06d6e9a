/* Filter Card Component Styles */
.filter-card {
  position: relative;
  background: linear-gradient(135deg, rgba(30, 11, 56, 0.8), rgba(58, 22, 112, 0.6));
  border-radius: 0.5rem;
  border: 1px solid rgba(122, 50, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  padding: 0.75rem;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
}

.filter-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 15px rgba(122, 50, 255, 0.3);
  border-color: rgba(122, 50, 255, 0.4);
}

.filter-card.active {
  border-color: #00E5FF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 229, 255, 0.4);
  background: linear-gradient(135deg, rgba(30, 11, 56, 0.9), rgba(58, 22, 112, 0.7));
}

.filter-card.active:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 229, 255, 0.5);
}

.filter-card.focused {
  outline: 2px solid #00E5FF;
  outline-offset: 2px;
}

.filter-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Filter Card Header */
.filter-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.filter-card-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.25px;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* Filter Card Content */
.filter-card-content {
  flex: 1;
  position: relative;
}

/* Select Button Styles */
.filter-select-button {
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.5rem 0.625rem;
  color: #ffffff;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: left;
  min-height: 32px;
}

.filter-select-button:hover {
  background: rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
}

.filter-select-button:focus {
  outline: none;
  border-color: #00E5FF;
  box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
}

.filter-select-button.open {
  border-color: #00E5FF;
  background: rgba(0, 0, 0, 0.5);
}

.filter-select-value {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ffffff;
}

.filter-select-arrow {
  font-size: 0.75rem;
  color: #00E5FF;
  transition: transform 0.2s ease;
  margin-left: 0.5rem;
}

.filter-select-arrow.rotated {
  transform: rotate(180deg);
}

/* Dropdown Styles */
.filter-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(30, 11, 56, 0.95);
  border: 1px solid rgba(122, 50, 255, 0.3);
  border-radius: 0.5rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  margin-top: 0.25rem;
  backdrop-filter: blur(10px);
  max-height: 200px;
  overflow-y: auto;
}

.filter-option {
  width: 100%;
  background: none;
  border: none;
  padding: 0.5rem 0.625rem;
  color: #ffffff;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  min-height: 32px;
  display: flex;
  align-items: center;
}

.filter-option:last-child {
  border-bottom: none;
}

.filter-option:hover {
  background: rgba(122, 50, 255, 0.2);
  color: #00E5FF;
}

.filter-option.selected {
  background: rgba(0, 229, 255, 0.2);
  color: #00E5FF;
  font-weight: 500;
}

.filter-option:focus {
  outline: none;
  background: rgba(122, 50, 255, 0.3);
}

/* Input Styles */
.filter-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.5rem 0.625rem;
  color: #ffffff;
  font-size: 0.75rem;
  transition: all 0.2s ease;
  min-height: 32px;
}

.filter-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.filter-input:hover {
  background: rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
}

.filter-input:focus {
  outline: none;
  border-color: #00E5FF;
  box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
  background: rgba(0, 0, 0, 0.5);
}

/* Active Indicator */
.filter-card-indicator {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  z-index: 10;
}

.indicator-dot {
  display: block;
  width: 8px;
  height: 8px;
  background: #00E5FF;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 229, 255, 0.6);
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .filter-card,
  .filter-select-button,
  .filter-input,
  .filter-option,
  .filter-select-arrow,
  .indicator-dot {
    transition: none;
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .filter-card {
    border-width: 2px;
  }

  .filter-card.active {
    border-width: 3px;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .filter-card {
    min-height: 90px;
    padding: 0.75rem;
  }

  .filter-card-header {
    margin-bottom: 0.5rem;
  }

  .filter-card-title {
    font-size: 0.8125rem;
    white-space: normal;
  }

  .filter-select-button,
  .filter-input {
    min-height: 36px;
    padding: 0.5rem 0.625rem;
    font-size: 0.8125rem;
  }

  .filter-option {
    padding: 0.5rem 0.625rem;
    font-size: 0.8125rem;
    min-height: 36px;
  }
}

@media (max-width: 480px) {
  .filter-card {
    min-height: 90px;
    padding: 0.75rem;
  }

  .filter-card-title {
    font-size: 0.75rem;
    letter-spacing: 0.25px;
  }

  .filter-select-button,
  .filter-input {
    min-height: 36px;
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .filter-option {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .filter-dropdown {
    max-height: 150px;
  }
}

/* Desktop optimizations for horizontal layout */
@media (min-width: 1200px) {
  .filter-card {
    min-height: 75px;
    max-width: none;
  }

  .filter-card-title {
    font-size: 0.6875rem;
    letter-spacing: 0.15px;
  }

  .filter-select-button,
  .filter-input {
    min-height: 28px;
    padding: 0.375rem 0.5rem;
    font-size: 0.6875rem;
  }

  .filter-option {
    padding: 0.375rem 0.5rem;
    font-size: 0.6875rem;
    min-height: 28px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .filter-card {
    min-height: 100px;
  }

  .filter-select-button,
  .filter-input {
    min-height: 44px; /* Larger touch targets */
    padding: 0.625rem;
  }

  .filter-option {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 0.625rem;
  }
}

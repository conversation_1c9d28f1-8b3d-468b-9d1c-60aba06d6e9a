from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import json
import os
import logging
from typing import List, Dict, Optional, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the workspace root directory
WORKSPACE_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
EVENTS_CONFIG_PATH = os.path.join(WORKSPACE_ROOT, "events_configuration.json")

# Create router
router = APIRouter(prefix="/api/augment", tags=["events"])

# Define models
class EventRule(BaseModel):
    id: int
    name: str
    enabled: bool
    hotlisted: bool
    show_popup: bool
    play_audio: bool

class EventRuleSet(BaseModel):
    rules: List[EventRule]

class DetectionRuleToggle(BaseModel):
    event: str
    enabled: bool

class EventStatistic(BaseModel):
    event_id: int
    event_name: str
    count: int
    camera_id: Optional[str] = None
    timestamp: str

class EventStatisticsResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None

# Helper function to ensure the events configuration file exists
def ensure_events_config():
    if not os.path.exists(EVENTS_CONFIG_PATH):
        # Create default configuration with all event types
        default_events = {
            "rules": [
                {"id": 1, "name": "Appearance Search", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 2, "name": "Camera Tamper", "enabled": True, "hotlisted": True, "show_popup": True, "play_audio": True},
                {"id": 3, "name": "Chain/Handbag Snatching", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 4, "name": "Crowd Detection", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 5, "name": "Eve Teasing", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 6, "name": "Face Capture", "enabled": True, "hotlisted": True, "show_popup": True, "play_audio": False},
                {"id": 7, "name": "Face Recognition", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 8, "name": "Gesture Detection", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 9, "name": "Graffiti and Vandalism Detection", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 10, "name": "Intrusion Detection", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 11, "name": "Lakshmanrekha Crossing", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 12, "name": "Loitering", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 13, "name": "Mobile Snatching", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 14, "name": "Object Classification", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 15, "name": "People Fighting", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 16, "name": "Person Collapsing", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 17, "name": "Strike / Morcha / Hartal / Procession", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 18, "name": "Suspected Appearance", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 19, "name": "Unattended Object", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 20, "name": "Women Surrounded by Men", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 21, "name": "Women/Infant Abduction", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 22, "name": "Vehicle Monitoring", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False},
                {"id": 23, "name": "Zone Monitoring", "enabled": False, "hotlisted": False, "show_popup": False, "play_audio": False}
            ],
            "statistics": []
        }

        with open(EVENTS_CONFIG_PATH, "w") as f:
            json.dump(default_events, f, indent=2)

        logger.info(f"Created default events configuration at {EVENTS_CONFIG_PATH}")

# Endpoint to get all event rules
@router.get("/events/rules")
async def get_event_rules():
    try:
        ensure_events_config()

        with open(EVENTS_CONFIG_PATH, "r") as f:
            events_data = json.load(f)

        return {
            "success": True,
            "data": {"rules": events_data["rules"]},
            "message": "Event rules retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting event rules: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Endpoint to update event rules
@router.post("/events/rules")
async def update_event_rules(rule_set: EventRuleSet):
    try:
        ensure_events_config()

        with open(EVENTS_CONFIG_PATH, "r") as f:
            events_data = json.load(f)

        # Update rules
        events_data["rules"] = [rule.dict() for rule in rule_set.rules]

        with open(EVENTS_CONFIG_PATH, "w") as f:
            json.dump(events_data, f, indent=2)

        return {
            "success": True,
            "message": "Event rules updated successfully"
        }
    except Exception as e:
        logger.error(f"Error updating event rules: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Endpoint to toggle a single detection rule
@router.post("/detection-rule")
async def toggle_detection_rule(rule_toggle: DetectionRuleToggle):
    try:
        ensure_events_config()

        with open(EVENTS_CONFIG_PATH, "r") as f:
            events_data = json.load(f)

        # Find the rule by name and update its enabled status
        rule_found = False
        for rule in events_data["rules"]:
            if rule["name"] == rule_toggle.event:
                rule["enabled"] = rule_toggle.enabled
                rule_found = True
                break

        if not rule_found:
            return {
                "success": False,
                "error": f"Rule '{rule_toggle.event}' not found"
            }

        # Save the updated configuration
        with open(EVENTS_CONFIG_PATH, "w") as f:
            json.dump(events_data, f, indent=2)

        return {
            "success": True,
            "message": f"Detection rule '{rule_toggle.event}' {'enabled' if rule_toggle.enabled else 'disabled'} successfully"
        }
    except Exception as e:
        logger.error(f"Error toggling detection rule: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Endpoint to get event statistics
@router.get("/events/statistics")
async def get_event_statistics(camera_id: Optional[str] = None, event_id: Optional[int] = None):
    try:
        ensure_events_config()

        # In a real application, this would query a database for actual statistics
        # For this example, we'll generate some mock data
        mock_statistics = [
            {"event_id": 1, "event_name": "Appearance Search", "count": 15, "camera_id": "camera1", "timestamp": "2023-05-08T10:30:00Z"},
            {"event_id": 2, "event_name": "Camera Tamper", "count": 3, "camera_id": "camera2", "timestamp": "2023-05-08T11:15:00Z"},
            {"event_id": 6, "event_name": "Face Capture", "count": 42, "camera_id": "camera1", "timestamp": "2023-05-08T12:00:00Z"},
            {"event_id": 10, "event_name": "Intrusion Detection", "count": 7, "camera_id": "camera3", "timestamp": "2023-05-08T13:45:00Z"},
            {"event_id": 19, "event_name": "Unattended Object", "count": 2, "camera_id": "camera2", "timestamp": "2023-05-08T14:30:00Z"}
        ]

        # Filter statistics based on query parameters
        filtered_stats = mock_statistics
        if camera_id:
            filtered_stats = [stat for stat in filtered_stats if stat["camera_id"] == camera_id]
        if event_id:
            filtered_stats = [stat for stat in filtered_stats if stat["event_id"] == event_id]

        return {
            "success": True,
            "data": {"statistics": filtered_stats},
            "message": "Event statistics retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting event statistics: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# RTSP Stream Configuration
# Copy this file to .env and modify values as needed

# Timeout settings (in seconds)
# These control how long to wait for RTSP connections and frame reads
RTSP_OPEN_TIMEOUT=60
RTSP_READ_TIMEOUT=180

# Connection retry settings
MAX_RECONNECT_ATTEMPTS=10
RECONNECT_BASE_DELAY=2
MAX_RECONNECT_DELAY=60

# Frame handling
MAX_FRAME_RETRIES=5
FRAME_TIMEOUT_THRESHOLD=60

# Buffer settings
BUFFER_SIZE_STABLE=3
BUFFER_SIZE_UNSTABLE=5

# Frame rate settings
TARGET_FPS_STABLE=24
TARGET_FPS_UNSTABLE=15

# Health check settings
HEALTH_CHECK_INTERVAL=30

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Stream monitoring
ENABLE_STREAM_MONITORING=true

# Auto-start streams on server startup
AUTO_START_STREAMS=true

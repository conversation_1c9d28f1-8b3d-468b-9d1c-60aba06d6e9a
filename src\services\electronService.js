// This service provides a wrapper around electron functionality
// with fallbacks for web environments

const isElectron = () => {
  // Detect if running in Electron
  return window && window.process && window.process.type === 'renderer';
};

// Get the ipcRenderer if we're in Electron, otherwise return a mock
const getIpcRenderer = () => {
  if (isElectron()) {
    return window.require('electron').ipcRenderer;
  }
  
  // Return a mock implementation for web environment
  return {
    invoke: (channel, ...args) => {
      console.warn(`IPC call "${channel}" not supported in web environment`);
      return Promise.reject(new Error('This feature is only available in the desktop app'));
    }
  };
};

export const ipcRenderer = getIpcRenderer();
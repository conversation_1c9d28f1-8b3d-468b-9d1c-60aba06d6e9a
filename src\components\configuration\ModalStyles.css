/*
 * Shared Modal Styles
 * Common styling for all modal dialogs in the Configuration section
 */

/* Modal Overlay - Semi-transparent backdrop */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Using black with opacity */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

/* Modal Container */
.modal-content {
  width: 500px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  border-radius: 0.75rem; /* Using ravia border-radius */
  background: linear-gradient(135deg, #FFFFFF, #F8F9FA); /* Using white gradient */
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2); /* Using black shadow */
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden; /* Ensures content doesn't overflow rounded corners */
  max-height: 70vh;
}

/* Modal Header */
.modal-header {
  height: 60px;
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* Using black with opacity */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  text-align: center;
  text-shadow: 0 0 10px rgba(0, 229, 255, 0.3);
}

.modal-close-button {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #FFFFFF;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.modal-close-button:hover {
  opacity: 1;
  background-color: rgba(0, 229, 255, 0.2);
  color: #00E5FF;
  box-shadow: 0 0 10px rgba(0, 229, 255, 0.3);
}

/* Modal Body */
.modal-body {
  background: rgba(0, 0, 0, 0.1);
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-body p {
  color: #FFFFFF;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

/* Custom Scrollbar */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(122, 50, 255, 0.1);
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(0, 229, 255, 0.3);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 229, 255, 0.5);
}

/* Form Elements */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid rgba(122, 50, 255, 0.3);
  border-radius: 4px;
  font-size: 14px;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.form-group input::placeholder,
.form-group select::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #00E5FF;
  box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
  background: rgba(0, 0, 0, 0.3);
}

/* Modal Footer */
.modal-footer {
  background: rgba(0, 0, 0, 0.2);
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 229, 255, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

/* Button Styles */
.primary-button {
  background: #FFFFFF; /* White background */
  color: #000000;
  height: 36px;
  min-width: 100px;
  border: 1px solid #000000;
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.primary-button:hover {
  transform: translateY(-2px);
  background-color: #F8F9FA;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
}

.secondary-button {
  background: rgba(0, 0, 0, 0.1);
  color: #000000;
  height: 36px;
  min-width: 100px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-button:hover {
  background: rgba(0, 0, 0, 0.15);
  border-color: rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

/* Error Message */
.error-message {
  color: #FF3D81; /* Using ravia-pink */
  background-color: rgba(255, 61, 129, 0.1);
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  border: 1px solid rgba(255, 61, 129, 0.2);
}

/* Responsive Adaptations */
@media (max-width: 600px) {
  .modal-content {
    width: 90%;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-header h3 {
    font-size: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
  }
}

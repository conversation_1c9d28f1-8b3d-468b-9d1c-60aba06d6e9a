/* Purple Sidebar Styling */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap');

/* Animation Keyframes */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Base Sidebar Styles */
.sidebar {
  width: 256px; /* 16rem = 256px */
  background: linear-gradient(135deg, #00299d 0%, #000000 100%);
  color: white;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  box-shadow: 0 0 15px rgba(0, 41, 157, 0.3);
  animation: slideIn 0.5s ease-out forwards;
  z-index: 100;
  font-family: 'Montserrat', sans-serif;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Logo Section */
.sidebar-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-logo {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.sidebar-header-title {
  font-size: 1.25rem;
  font-weight: 700;
  text-shadow: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #000000;
}

/* Sidebar Content */
.sidebar-content {
  padding: 1rem;
}

/* Sidebar Section */
.sidebar-section {
  margin-bottom: 0.75rem;
}

/* Sidebar Button */
.sidebar-btn {
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  transform-origin: left center;
}

.sidebar-btn:hover {
  transform: scale(1.05);
  background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-btn.active {
  background-color: rgba(0, 0, 0, 0.15);
  border-left: 4px solid #000000;
  font-weight: 600;
}

/* Icon Container */
.sidebar-icon-container {
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.sidebar-icon {
  width: 1rem;
  height: 1rem;
  object-fit: contain;
}

.sidebar-icon img,
.sidebar-icon svg {
  width: 14px;
  height: 14px;
  object-fit: contain;
  display: block;
  color: currentColor;
  fill: currentColor;
}

/* Dropdown */
.sidebar-dropdown {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  margin: 0.25rem 0 0.5rem 0.5rem;
  overflow: hidden;
  padding: 0.5rem;
}

/* Sub Button */
.sidebar-btn-sub {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
  font-size: 0.8125rem;
  padding-left: 2.5rem;
  position: relative;
}

.sidebar-btn-sub:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-btn-sub.active {
  background-color: rgba(0, 0, 0, 0.15);
  border-left: 4px solid #000000;
}

/* Counts */
.collection-count,
.bookmark-count {
  margin-left: auto;
  font-size: 0.75rem;
  background-color: rgba(0, 0, 0, 0.1);
  color: #000000;
  border-radius: 9999px;
  padding: 0.125rem 0.5rem;
}

/* Notification Dot */
.notification-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  margin-left: auto;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.6);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

/* Divider */
.sidebar-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

/* Section Title */
.sidebar-section-title {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 0.75rem;
  padding: 0 0.5rem;
}

/* Chevron Icon */
.chevron-icon {
  margin-left: auto;
  font-size: 0.75rem;
  transition: transform 0.3s ease;
}

.chevron-icon.expanded {
  transform: rotate(180deg);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
    transition: width 0.3s ease;
  }

  .sidebar.expanded {
    width: 256px;
  }

  .sidebar-header-title,
  .sidebar-btn span,
  .collection-count,
  .bookmark-count,
  .chevron-icon {
    display: none;
  }

  .sidebar.expanded .sidebar-header-title,
  .sidebar.expanded .sidebar-btn span,
  .sidebar.expanded .collection-count,
  .sidebar.expanded .bookmark-count,
  .sidebar.expanded .chevron-icon {
    display: inline-flex;
  }

  .sidebar-btn {
    justify-content: center;
    padding: 0.75rem;
  }

  .sidebar.expanded .sidebar-btn {
    justify-content: flex-start;
  }

  .sidebar-dropdown {
    display: none;
  }

  .sidebar.expanded .sidebar-dropdown {
    display: block;
  }

  .sidebar-tooltip {
    position: absolute;
    left: 60px;
    background-color: #000000;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    white-space: nowrap;
    z-index: 1000;
  }

  .sidebar-btn:hover .sidebar-tooltip {
    opacity: 1;
    visibility: visible;
  }
}

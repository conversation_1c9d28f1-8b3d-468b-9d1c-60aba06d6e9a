<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Test Collections Cache</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        #output {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Clear Test Collections Cache</h1>

    <p>This tool will clear the <code>test_collection</code> entries from your browser's localStorage,
    specifically removing entries for IP addresses ************ and ************.</p>

    <p><strong>Important:</strong> This will also clean up menu items showing test_collection in the UI.</p>

    <p>Click the button below to run the cleanup script:</p>

    <button id="clearButton" class="button">Clear Test Collections</button>

    <div id="output">
        <p>Output will appear here...</p>
    </div>

    <h2>Alternative Method: Using Browser Console</h2>

    <p>If the button doesn't work, you can also run the script directly in your browser's console:</p>

    <ol>
        <li>Open your browser's developer tools (F12 or right-click and select "Inspect")</li>
        <li>Go to the "Console" tab</li>
        <li>Copy and paste the following code:</li>
    </ol>

    <pre id="codeSnippet"></pre>

    <script>
        // Load the script content
        fetch('clear_test_collections.js')
            .then(response => response.text())
            .then(text => {
                document.getElementById('codeSnippet').textContent = text;
            })
            .catch(error => {
                document.getElementById('codeSnippet').textContent =
                    `Error loading script: ${error}\n\nPlease make sure clear_test_collections.js is in the same directory.`;
            });

        // Set up the clear button
        document.getElementById('clearButton').addEventListener('click', function() {
            const outputDiv = document.getElementById('output');
            outputDiv.innerHTML = '<p>Running cleanup script...</p>';

            // Capture console.log output
            const originalLog = console.log;
            const originalError = console.error;
            const logs = [];

            console.log = function() {
                logs.push(['log', Array.from(arguments).join(' ')]);
                originalLog.apply(console, arguments);
            };

            console.error = function() {
                logs.push(['error', Array.from(arguments).join(' ')]);
                originalError.apply(console, arguments);
            };

            // Run the script
            try {
                const result = clearTestCollections();

                // Display the logs
                let output = '<h3>Results:</h3><ul>';
                logs.forEach(log => {
                    const type = log[0];
                    const message = log[1];
                    output += `<li class="${type}">${message}</li>`;
                });
                output += '</ul>';

                if (result) {
                    output += '<p><strong>✅ Cleanup completed successfully!</strong></p>';
                    output += '<p>Please refresh your VMS application to see the changes.</p>';

                    // Add specific instructions for menu items
                    const menuItems = document.querySelectorAll('.submenu-label span');
                    let foundTestCollection = false;
                    menuItems.forEach(item => {
                      if (item.textContent.toLowerCase().includes('test_collection')) {
                        foundTestCollection = true;
                      }
                    });

                    if (foundTestCollection) {
                      output += '<div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeeba; border-radius: 4px;">';
                      output += '<p><strong>⚠️ Note:</strong> Test collection menu items were found in the current UI.</p>';
                      output += '<p>Please <strong>completely close and restart</strong> your application to remove these menu items.</p>';
                      output += '</div>';
                    }
                } else {
                    output += '<p><strong>⚠️ No changes were made.</strong></p>';
                    output += '<p>Either no test collections were found or there was an error.</p>';
                }

                outputDiv.innerHTML = output;
            } catch (error) {
                outputDiv.innerHTML = `<p>Error running script: ${error.message}</p>`;
                console.error = originalError;
                console.error(error);
            }

            // Restore console functions
            console.log = originalLog;
            console.error = originalError;
        });
    </script>

    <script src="clear_test_collections.js"></script>
</body>
</html>

import React from 'react';
import { useArchiveStore } from '../../store/archiveStore';
import './RecordingStatusIndicator.css';

const RecordingStatusIndicator = ({ streamId, showLabel = true, size = 'medium' }) => {
  const { getRecordingStatus } = useArchiveStore();
  
  if (!streamId) return null;
  
  const status = getRecordingStatus(streamId);
  
  const getStatusConfig = () => {
    switch (status.status) {
      case 'recording':
        return {
          className: 'recording-active',
          icon: '🔴',
          label: 'Recording',
          color: '#ff4444',
          pulse: true
        };
      case 'stopped':
        return {
          className: 'recording-stopped',
          icon: '⏹️',
          label: 'Stopped',
          color: '#666666',
          pulse: false
        };
      case 'stale':
        return {
          className: 'recording-stale',
          icon: '⚠️',
          label: 'Connection Lost',
          color: '#ff9900',
          pulse: false
        };
      default:
        return {
          className: 'recording-unknown',
          icon: '❓',
          label: 'Unknown',
          color: '#888888',
          pulse: false
        };
    }
  };
  
  const config = getStatusConfig();
  
  return (
    <div 
      className={`recording-status-indicator ${config.className} size-${size}`}
      title={`Recording Status: ${config.label}`}
      aria-label={`Recording status: ${config.label}`}
    >
      <div 
        className={`status-dot ${config.pulse ? 'pulse' : ''}`}
        style={{ backgroundColor: config.color }}
      />
      {showLabel && (
        <span className="status-label">{config.label}</span>
      )}
    </div>
  );
};

// Component for showing overall recording status
export const OverallRecordingStatus = () => {
  const { 
    archiveStatus, 
    getActiveRecordingCount, 
    hasActiveRecordings,
    isLoading 
  } = useArchiveStore();
  
  if (isLoading) {
    return (
      <div className="overall-recording-status loading">
        <div className="status-dot loading-dot" />
        <span className="status-text">Checking status...</span>
      </div>
    );
  }
  
  if (!archiveStatus) {
    return (
      <div className="overall-recording-status error">
        <div className="status-dot error-dot" />
        <span className="status-text">Archive system unavailable</span>
      </div>
    );
  }
  
  const activeCount = getActiveRecordingCount();
  const hasActive = hasActiveRecordings();
  
  return (
    <div className={`overall-recording-status ${hasActive ? 'active' : 'inactive'}`}>
      <div className={`status-dot ${hasActive ? 'pulse' : ''}`} />
      <span className="status-text">
        {hasActive 
          ? `Recording ${activeCount} stream${activeCount !== 1 ? 's' : ''}`
          : 'No active recordings'
        }
      </span>
      {archiveStatus.active_recordings !== undefined && (
        <span className="status-detail">
          ({archiveStatus.active_recordings} process{archiveStatus.active_recordings !== 1 ? 'es' : ''} running)
        </span>
      )}
    </div>
  );
};

// Component for camera list with recording status
export const CameraRecordingStatus = ({ cameras = [] }) => {
  const { getRecordingStatus } = useArchiveStore();
  
  if (!cameras.length) {
    return (
      <div className="camera-recording-status empty">
        <p>No cameras configured</p>
      </div>
    );
  }
  
  return (
    <div className="camera-recording-status">
      <h4>Camera Recording Status</h4>
      <div className="camera-status-list">
        {cameras.map(camera => {
          const streamId = `${camera.collection}_${camera.ip}`;
          const status = getRecordingStatus(streamId);
          
          return (
            <div key={camera.id} className="camera-status-item">
              <div className="camera-info">
                <span className="camera-name">{camera.name}</span>
                <span className="camera-ip">{camera.ip}</span>
              </div>
              <RecordingStatusIndicator 
                streamId={streamId} 
                showLabel={true}
                size="small"
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Hook for using recording status in other components
export const useRecordingStatus = (streamId) => {
  const { getRecordingStatus } = useArchiveStore();
  return getRecordingStatus(streamId);
};

export default RecordingStatusIndicator;

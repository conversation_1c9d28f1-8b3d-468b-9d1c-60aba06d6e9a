import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DraggableSidebarCamera from '../DraggableSidebarCamera';

// Mock react-dnd
jest.mock('react-dnd', () => ({
  useDrag: () => [{ isDragging: false }, jest.fn()],
}));

const mockCamera = {
  id: 'camera-1',
  name: 'Test Camera',
  ip: '*************',
  streamUrl: 'rtsp://*************:554/stream',
  collectionId: 'collection-1'
};

describe('DraggableSidebarCamera', () => {
  it('renders camera name correctly', () => {
    render(
      <DraggableSidebarCamera
        camera={mockCamera}
        onClick={jest.fn()}
      />
    );

    expect(screen.getByText('Test Camera')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const mockOnClick = jest.fn();
    render(
      <DraggableSidebarCamera
        camera={mockCamera}
        onClick={mockOnClick}
      />
    );

    fireEvent.click(screen.getByRole('button'));
    expect(mockOnClick).toHaveBeenCalledWith(mockCamera);
  });

  it('handles keyboard navigation', () => {
    const mockOnClick = jest.fn();
    render(
      <DraggableSidebarCamera
        camera={mockCamera}
        onClick={mockOnClick}
      />
    );

    const button = screen.getByRole('button');
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(mockOnClick).toHaveBeenCalledWith(mockCamera);

    fireEvent.keyDown(button, { key: ' ' });
    expect(mockOnClick).toHaveBeenCalledTimes(2);
  });

  it('applies bookmarked class when isBookmarked is true', () => {
    render(
      <DraggableSidebarCamera
        camera={mockCamera}
        isBookmarked={true}
        onClick={jest.fn()}
      />
    );

    expect(screen.getByRole('button')).toHaveClass('bookmarked');
  });

  it('has proper accessibility attributes', () => {
    render(
      <DraggableSidebarCamera
        camera={mockCamera}
        onClick={jest.fn()}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Drag Test Camera to video grid or click to select');
    expect(button).toHaveAttribute('tabIndex', '0');
  });
});

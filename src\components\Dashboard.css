.dashboard {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  overflow: hidden;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.camera-search-container {
  flex: 1;
  max-width: 400px;
  margin-right: 1rem;
}

.camera-search-input {
  width: 100%;
  padding: 0.5rem 1rem;
  font-size: 14px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  outline: none;
  transition: border-color 0.2s ease;
}

.camera-search-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.camera-search-input::placeholder {
  color: #999;
}

.sidebar-btn {
  width: 100%;
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  text-align: left;
  padding: 10px 0;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  border-radius: 4px;
}

.sidebar-btn:hover,
.sidebar-btn:focus {
  background: #262626;
  color: #E37814;
  outline: none;
}
.sidebar-dropdown {
  margin-left: 16px;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
}
.sidebar-btn-sub {
  font-size: 15px;
  padding-left: 24px;
  background: none;
  border: none;
  color: inherit;
  text-align: left;
  transition: background 0.2s, color 0.2s;
}
.sidebar-btn-sub:hover,
.sidebar-btn-sub:focus {
  background: #232323;
  color: #E37814;
  outline: none;
}

.events-content {
  padding: 0;
  background: #181e23;
}

.events-filters-bar {
  display: flex;
  align-items: center;
  padding: 12px 16px 8px 16px;
  background: #181e23;
  border-bottom: 1px solid #232323;
  gap: 8px;
}

.events-filter-btn {
  background: #232c33;
  color: #fff;
  border: 1px solid #232c33;
  border-radius: 3px;
  padding: 4px 12px;
  font-size: 14px;
  margin-right: 2px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
}

.events-filter-btn-main {
  font-weight: bold;
  background: #181e23;
  border: none;
  color: #b6e14b;
}

.events-filter-btn-refresh {
  background: #232c33;
  color: #6ec1e4;
  border: none;
  margin-right: 16px;
}

.events-filter-btn-active,
.events-filter-btn:hover {
  background: #2176ff;
  color: #fff;
}

.events-filter-input {
  margin-left: 6px;
  background: transparent;
  border: 1px solid #444;
  color: #fff;
  border-radius: 2px;
  height: 20px;
  padding: 0 4px;
}

.video-cell {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
  background-color: #2a2a2a;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 0; /* Important for flex child to shrink properly */
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #000;
}

.camera-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
}

.camera-name {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.controls {
  display: flex;
  gap: 8px;
}

.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.control-button:hover {
  transform: scale(1.1);
}

.control-button img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .video-cell {
    min-height: 150px;
  }
  
  .camera-name {
    font-size: 12px;
  }
  
  .control-button img {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .video-cell {
    min-height: 120px;
  }
  
  .camera-name {
    font-size: 11px;
  }
  
  .control-button img {
    width: 12px;
    height: 12px;
  }
} 
#!/usr/bin/env python3
"""
Comprehensive video corruption diagnostic tool
"""

import subprocess
import json
from pathlib import Path
import os
import time

def check_ffmpeg_availability():
    """Check if FFmpeg is available and working"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFmpeg is available")
            # Extract version info
            version_line = result.stdout.split('\n')[0]
            print(f"  Version: {version_line}")
            return True
        else:
            print("✗ FFmpeg not working properly")
            return False
    except FileNotFoundError:
        print("✗ FFmpeg not found in PATH")
        return False
    except Exception as e:
        print(f"✗ Error checking FFmpeg: {e}")
        return False

def analyze_video_file(file_path):
    """Analyze video file with FFprobe"""
    print(f"\n=== Analyzing {file_path.name} ===")
    
    # Basic file info
    if not file_path.exists():
        print("✗ File does not exist")
        return None
    
    file_size = file_path.stat().st_size
    print(f"File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    # Check file modification time
    mtime = file_path.stat().st_mtime
    current_time = time.time()
    age_seconds = current_time - mtime
    print(f"File age: {age_seconds:.1f} seconds ({age_seconds/60:.1f} minutes)")
    
    # Check if file is currently being written to
    if age_seconds < 30:
        print("⚠️  File was modified very recently - may still be recording")
    
    # Read first and last bytes to check for truncation
    try:
        with open(file_path, 'rb') as f:
            first_32 = f.read(32)
            print(f"First 32 bytes: {first_32.hex()}")
            
            # Check MP4 signature
            if b'ftyp' in first_32[:20]:
                print("✓ Valid MP4 signature found")
            else:
                print("✗ No valid MP4 signature")
            
            # Check end of file
            f.seek(-32, 2)  # Seek to 32 bytes from end
            last_32 = f.read(32)
            print(f"Last 32 bytes: {last_32.hex()}")
            
    except Exception as e:
        print(f"✗ Error reading file: {e}")
        return None
    
    # Use FFprobe to analyze
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(file_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            print("✓ FFprobe analysis successful")
            
            # Format info
            if 'format' in data:
                fmt = data['format']
                print(f"  Container: {fmt.get('format_name', 'unknown')}")
                print(f"  Duration: {fmt.get('duration', 'unknown')} seconds")
                print(f"  Bitrate: {fmt.get('bit_rate', 'unknown')} bps")
                print(f"  Size: {fmt.get('size', 'unknown')} bytes")
            
            # Stream info
            if 'streams' in data:
                for i, stream in enumerate(data['streams']):
                    codec_type = stream.get('codec_type', 'unknown')
                    codec_name = stream.get('codec_name', 'unknown')
                    print(f"  Stream {i}: {codec_type} ({codec_name})")
                    
                    if codec_type == 'video':
                        width = stream.get('width', 0)
                        height = stream.get('height', 0)
                        fps = stream.get('r_frame_rate', '0/1')
                        print(f"    Resolution: {width}x{height}")
                        print(f"    Frame rate: {fps}")
            
            return data
        else:
            print("✗ FFprobe failed")
            print(f"  Error: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("✗ FFprobe timed out - file may be corrupted")
        return None
    except Exception as e:
        print(f"✗ FFprobe error: {e}")
        return None

def check_file_integrity(file_path):
    """Check if file can be processed by FFmpeg"""
    print(f"\n=== Integrity Check for {file_path.name} ===")
    
    try:
        # Try to extract first frame
        cmd = [
            'ffmpeg', '-i', str(file_path), '-vframes', '1', 
            '-f', 'null', '-', '-v', 'error'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ File can be processed by FFmpeg")
            return True
        else:
            print("✗ FFmpeg cannot process file")
            print(f"  Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ FFmpeg processing timed out")
        return False
    except Exception as e:
        print(f"✗ Error testing file integrity: {e}")
        return False

def check_recording_processes():
    """Check if there are active recording processes"""
    print("\n=== Active Recording Processes ===")
    
    try:
        # Check for FFmpeg processes
        if os.name == 'nt':  # Windows
            cmd = ['tasklist', '/FI', 'IMAGENAME eq ffmpeg.exe']
        else:  # Unix-like
            cmd = ['pgrep', '-f', 'ffmpeg']
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            print("⚠️  Active FFmpeg processes found:")
            print(result.stdout)
        else:
            print("✓ No active FFmpeg processes")
            
    except Exception as e:
        print(f"Error checking processes: {e}")

def main():
    """Main diagnostic function"""
    print("=== Video Corruption Diagnostic Tool ===\n")
    
    # Check FFmpeg
    if not check_ffmpeg_availability():
        print("Cannot proceed without FFmpeg")
        return
    
    # Check for active recording processes
    check_recording_processes()
    
    # Find recording files
    recordings_dir = Path('./recordings')
    if not recordings_dir.exists():
        print(f"Recordings directory not found: {recordings_dir}")
        return
    
    print(f"\nScanning recordings directory: {recordings_dir}")
    
    # Find all MP4 files
    mp4_files = []
    for stream_dir in recordings_dir.iterdir():
        if stream_dir.is_dir():
            for mp4_file in stream_dir.glob('*.mp4'):
                mp4_files.append(mp4_file)
    
    if not mp4_files:
        print("No MP4 files found")
        return
    
    print(f"Found {len(mp4_files)} MP4 files")
    
    # Sort by modification time (newest first)
    mp4_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
    
    # Analyze the newest files (up to 3)
    for mp4_file in mp4_files[:3]:
        data = analyze_video_file(mp4_file)
        if data:
            check_file_integrity(mp4_file)
    
    print("\n=== Summary ===")
    print("If files show valid MP4 signature but cannot be played:")
    print("1. File may be incomplete (still recording)")
    print("2. File may be corrupted during recording")
    print("3. FFmpeg process may have been terminated improperly")
    print("4. Disk space or permission issues")
    print("\nRecommendations:")
    print("- Stop all recording processes before testing")
    print("- Check disk space and permissions")
    print("- Consider adding proper file finalization to recording process")

if __name__ == "__main__":
    main()

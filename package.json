{"name": "react-electron-app", "version": "1.0.0", "main": "main.js", "scripts": {"start": "set ELECTRON_START_URL=http://localhost:3000 && concurrently \"cross-env BROWSER=none npm run react-start\" \"wait-on http://localhost:3000 && electron .\"", "react-start": "webpack serve --config webpack.config.js", "build": "webpack --config webpack.config.js --mode production", "test": "jest", "test:coverage": "jest --coverage"}, "build": {"appId": "com.electron.app", "win": {"icon": "build/EAGLE.ico"}}, "keywords": [], "author": "", "license": "ISC", "description": "A simple React Electron desktop application", "dependencies": {"@electron/remote": "^2.0.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource-variable/inter": "^5.2.5", "@fontsource/inter": "^5.2.5", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@react-google-maps/api": "^2.20.6", "autoprefixer": "^10.4.21", "d3": "^7.9.0", "electron": "^28.0.0", "electron-builder": "^24.6.4", "framer-motion": "^10.16.4", "lucide-react": "^0.525.0", "postcss": "^8.5.3", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "simple-peer": "^9.11.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.6", "video.js": "^8.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "babel-jest": "^29.7.0", "babel-loader": "^9.2.1", "concurrently": "^8.2.1", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "electron-is-dev": "^3.0.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "path-browserify": "^1.0.1", "style-loader": "^3.3.3", "url-loader": "^4.1.1", "wait-on": "^7.0.1", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}
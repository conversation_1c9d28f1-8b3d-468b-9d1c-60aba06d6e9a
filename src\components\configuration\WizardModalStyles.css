/*
 * Wizard <PERSON> Styles
 * Styling for step-based wizard modals in the Configuration section
 */

/* Modal Overlay - Semi-transparent backdrop */
.wizard-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Modal Container */
.wizard-modal-content {
  width: 600px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  overflow: hidden; /* Ensures content doesn't overflow rounded corners */
  max-height: 80vh;
  background-color: #FFFFFF;
}

/* Modal Header - Black with gold text */
.wizard-modal-header {
  background-color: #000000;
  padding: 20px 20px 5px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.wizard-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #672491;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.wizard-modal-close-button {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #FFFFFF;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.wizard-modal-close-button:hover {
  opacity: 1;
}

/* Progress Indicator */
.wizard-progress-container {
  padding: 10px 20px;
  background-color: #F5F5F5;
}

.wizard-progress-text {
  font-size: 14px;
  color: #333333;
  margin-bottom: 5px;
  font-weight: 500;
}

.wizard-progress-bar {
  height: 8px;
  background-color: #E0E0E0;
  border-radius: 4px;
  overflow: hidden;
}

.wizard-progress-fill {
  height: 100%;
  background-color: #000000;
  transition: width 0.3s ease;
}

/* Tab Navigation */
.wizard-tabs {
  display: flex;
  background-color: #F5F5F5;
  border-bottom: 1px solid #E0E0E0;
}

.wizard-tab {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.wizard-tab:hover {
  background-color: #EEEEEE;
}

.wizard-tab.active {
  color: 000000eb;
  border-bottom-color:#b6e14b;
  background-color: #FFFFFF;
}

/* Modal Body */
.wizard-modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

/* Tab Content */
.wizard-tab-content {
  display: none;
}

.wizard-tab-content.active {
  display: block;
}

/* Form Elements */
.wizard-form-group {
  margin-bottom: 16px;
}

.wizard-form-group label {
  display: block;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.wizard-form-group input,
.wizard-form-group select {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #CCCCCC;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
}

.wizard-form-group input::placeholder,
.wizard-form-group select::placeholder {
  color: #AAAAAA;
}

.wizard-form-group input:focus,
.wizard-form-group select:focus {
  outline: none;
  border-color: #b6e14b;
  box-shadow: 0 0 0 2px rgba(255, 194, 51, 0.2);
}

/* Modal Footer */
.wizard-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
}

.wizard-left-footer {
  display: flex;
  gap: 12px;
}

.wizard-right-footer {
  display: flex;
  gap: 12px;
}

/* Navigation Buttons */
.wizard-nav-button {
  background-color: #FFFFFF;
  color: #000000;
  padding: 10px 60px;
  border: 1px solid #000000;
  border-radius: 4px;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.wizard-nav-button:hover {
  background-color: #E6E6E6;
  color: #333333;
}

.wizard-nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.wizard-nav-button.disabled:hover {
  background: #FFFFFF; /* White background */
  color: #000000;
}

/* Error Message */
.wizard-error-message {
  color: #FF3D81; /* Using ravia-pink */
  background-color: rgba(255, 61, 129, 0.1);
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  border: 1px solid rgba(255, 61, 129, 0.2);
}

/* Range Processing Overlay */
.range-processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.range-processing-content {
  text-align: center;
  padding: 40px;
  background-color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.range-processing-content h4 {
  margin: 0 0 20px 0;
  color: #333333;
  font-size: 18px;
  font-weight: 600;
}

.range-progress-bar {
  width: 100%;
  height: 12px;
  background-color: #E0E0E0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

.range-progress-fill {
  height: 100%;
  background-color: #000000;
  transition: width 0.3s ease;
  border-radius: 6px;
}

.range-processing-content p {
  margin: 0;
  color: #666666;
  font-size: 14px;
}

/* Range Results Section */
.range-results-section {
  background-color: #F8F9FA;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.range-results-section h4 {
  margin: 0 0 16px 0;
  color: #333333;
  font-size: 16px;
  font-weight: 600;
}

.result-group {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.result-group.success {
  background-color: #F0F8F0;
  border-left-color: #28A745;
}

.result-group.warning {
  background-color: #FFF8E1;
  border-left-color: #FFC107;
}

.result-group.error {
  background-color: #FFF0F0;
  border-left-color: #DC3545;
}

.result-group h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.result-group.success h5 {
  color: #155724;
}

.result-group.warning h5 {
  color: #856404;
}

.result-group.error h5 {
  color: #721C24;
}

.result-group ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.result-group li {
  margin-bottom: 4px;
  font-size: 13px;
  color: #555555;
}

.result-actions {
  margin-top: 20px;
  text-align: center;
}

/* Responsive Adaptations */
@media (max-width: 768px) {
  .wizard-modal-content {
    width: 90%;
  }

  .wizard-modal-body {
    padding: 16px;
  }

  .wizard-modal-header h3 {
    font-size: 16px;
  }

  .wizard-tab {
    padding: 10px 8px;
    font-size: 12px;
  }

  .wizard-nav-button {
    padding: 8px 20px;
  }

  .range-processing-content {
    padding: 20px;
    min-width: 250px;
  }

  .range-results-section {
    padding: 16px;
  }
}

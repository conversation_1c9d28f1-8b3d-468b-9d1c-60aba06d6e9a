<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Force Reset</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1 {
            color: #d32f2f;
            border-bottom: 2px solid #d32f2f;
            padding-bottom: 10px;
        }
        .warning {
            background-color: #ffebee;
            border-left: 4px solid #d32f2f;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .button {
            background-color: #d32f2f;
            border: none;
            color: white;
            padding: 12px 24px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 20px 0;
            cursor: pointer;
            border-radius: 4px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #b71c1c;
        }
        pre {
            background-color: #263238;
            color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
        }
        .steps {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .steps h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .steps ol {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <h1>VMS Force Reset Tool</h1>
    
    <div class="warning">
        <h2>⚠️ WARNING</h2>
        <p>This tool will completely reset the VMS application state by:</p>
        <ul>
            <li>Clearing <strong>ALL</strong> application data from localStorage</li>
            <li>Resetting all application state stores</li>
            <li>Forcing a hard reload of the application</li>
        </ul>
        <p>This is a <strong>last resort</strong> solution when other cleanup methods have failed.</p>
    </div>
    
    <div class="steps">
        <h3>How to use this tool:</h3>
        <ol>
            <li>Make sure your backend server is running</li>
            <li>Click the "Force Reset VMS" button below</li>
            <li>Wait for the application to reload</li>
            <li>Check if the test_collection entries are gone</li>
            <li>If issues persist, restart the backend server as well</li>
        </ol>
    </div>
    
    <button id="resetButton" class="button">Force Reset VMS</button>
    
    <h3>Alternative Method: Using Browser Console</h3>
    
    <p>If the button doesn't work, you can run this script directly in your browser's console:</p>
    
    <pre id="codeSnippet"></pre>
    
    <script>
        // Load the script content
        fetch('force_reset.js')
            .then(response => response.text())
            .then(text => {
                document.getElementById('codeSnippet').textContent = text;
            })
            .catch(error => {
                document.getElementById('codeSnippet').textContent = 
                    `Error loading script: ${error}\n\nPlease make sure force_reset.js is in the same directory.`;
            });
        
        // Set up the reset button
        document.getElementById('resetButton').addEventListener('click', function() {
            if (confirm("Are you sure you want to completely reset the VMS application state? This will clear all cached data.")) {
                // Load and execute the reset script
                const script = document.createElement('script');
                script.src = 'force_reset.js';
                document.body.appendChild(script);
            }
        });
    </script>
</body>
</html>

.camera-card {
  background-color: #1E0B38; /* Using ravia-purple-900 */
  border-radius: 0.75rem; /* Using ravia border-radius */
  overflow: hidden;
  box-shadow: 0 0 15px rgba(122, 50, 255, 0.5); /* Using ravia-glow */
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100%;
  cursor: pointer;
  position: relative;
  border: 1px solid rgba(122, 50, 255, 0.2);
}

.camera-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 0 20px rgba(122, 50, 255, 0.7);
  border: 1px solid rgba(122, 50, 255, 0.4);
}

.camera-card.bookmarked {
  border: 2px solid #00E5FF; /* Using ravia-cyan-500 */
  box-shadow: 0 0 15px rgba(0, 229, 255, 0.5); /* Using ravia-glow-cyan */
}

.camera-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: linear-gradient(to right, #1E0B38, #3A1670); /* Using ravia gradient */
  color: white;
  z-index: 2;
}

.camera-name {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.camera-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Recording Status Indicator Styles */
.recording-status-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-indicator.recording {
  background-color: #ff4444;
  box-shadow: 0 0 8px rgba(255, 68, 68, 0.6);
  animation: pulse-recording 2s infinite;
}

.status-indicator.stopped {
  background-color: #666666;
  box-shadow: 0 0 4px rgba(102, 102, 102, 0.4);
}

.status-indicator.backend-unavailable {
  background-color: #888888;
  box-shadow: 0 0 4px rgba(136, 136, 136, 0.4);
  opacity: 0.6;
}

.recording-status-dot.stale .status-indicator {
  background-color: #ff9900;
  box-shadow: 0 0 6px rgba(255, 153, 0, 0.5);
}

@keyframes pulse-recording {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.bookmark-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, opacity 0.2s;
  color: #00E5FF; /* Using ravia-cyan-500 */
}

.bookmark-button:hover {
  transform: scale(1.1);
  color: #7A32FF; /* Using ravia-purple-300 */
}

.bookmark-button.bookmarked {
  position: relative;
  color: #00E5FF; /* Using ravia-cyan-500 */
}

.bookmark-button.bookmarked::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #00E5FF; /* Using ravia-cyan-500 */
  box-shadow: 0 0 15px rgba(0, 229, 255, 0.5); /* Using ravia-glow-cyan */
}



.camera-card-content {
  flex: 1;
  position: relative;
  aspect-ratio: 16/9;
  background-color: #1E0B38; /* Using ravia-purple-900 */
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
}

.camera-offline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  gap: 10px;
  z-index: 1;
}

.camera-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(to right, #1E0B38, #2A0F4C); /* Using ravia gradient */
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.camera-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.camera-status::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #00E5FF; /* Using ravia-cyan-500 */
}



.camera-card:has(.camera-offline) .camera-status::before {
  background-color: rgba(255, 255, 255, 0.4);
}

.camera-ip {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
}
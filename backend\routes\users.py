from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import json
import os
import logging
import re
from typing import List, Optional, Dict

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the backend directory
BACKEND_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
USERS_CONFIG_PATH = os.path.join(BACKEND_DIR, "data/users.json")

# Store the last modification time of the users.json file
last_modified_time = 0
# Store the cached configuration
cached_config = None

# Create router
router = APIRouter(prefix="/api/augment/users", tags=["users"])

# Define models
class UserPermissions(BaseModel):
    searchCameras: bool = False
    configureCameras: bool = False
    createSchedules: bool = False
    ptzControl: bool = False
    manageStorage: bool = False
    manageUser: bool = False
    actOnEvents: bool = False
    archivePlay: bool = False
    locationGroupConfig: bool = False
    clipDownload: bool = False
    reportDownload: bool = False
    unmaskedLivePlay: bool = False
    unmaskedArchivePlay: bool = False

class User(BaseModel):
    username: str
    password: str
    role: str
    permissions: UserPermissions
    created_by: Optional[str] = None

class UserResponse(BaseModel):
    id: int
    username: str
    role: str
    permissions: Dict[str, bool]

class LoginRequest(BaseModel):
    username: str
    password: str
    role: Optional[str] = None

class LoginResponse(BaseModel):
    success: bool
    data: Optional[dict] = None
    error: Optional[str] = None

# Helper function to load users configuration
def load_users_config():
    global last_modified_time, cached_config

    try:
        # Check if users.json exists
        if not os.path.exists(USERS_CONFIG_PATH):
            logger.warning(f"Users configuration file not found at {USERS_CONFIG_PATH}")
            logger.warning("Please create a users.json file with at least one SuperAdmin user")
            # Return an empty configuration
            return {"users": []}

        # Check if the file has been modified since the last load
        current_modified_time = os.path.getmtime(USERS_CONFIG_PATH)

        # If we have a cached config and the file hasn't been modified, return the cached config
        if cached_config is not None and current_modified_time <= last_modified_time:
            return cached_config

        # Load the existing configuration
        logger.info(f"Loading users configuration from {USERS_CONFIG_PATH} (modified at {current_modified_time})")
        with open(USERS_CONFIG_PATH, "r") as f:
            data = json.load(f)

            # If the data is a list, convert it to the expected format
            if isinstance(data, list):
                data = {"users": data}

            # Update the last modified time and cache the config
            last_modified_time = current_modified_time
            cached_config = data

            return data
    except Exception as e:
        logger.error(f"Error loading users configuration: {str(e)}")
        # Return an empty configuration if there's an error
        return {"users": []}

# Helper function to save users configuration
def save_users_config(config):
    global last_modified_time, cached_config

    try:
        # Always save in the format {"users": [...]}
        if isinstance(config, dict) and "users" in config:
            data_to_save = {"users": config["users"]}
            logger.info(f"Saving users configuration with {len(config['users'])} users")
        else:
            # If it's a list, wrap it in the users key
            data_to_save = {"users": config}
            logger.info(f"Saving users configuration with {len(config)} users (from list)")

        # Log user details being saved
        user_info = [f"{u.get('username')} (ID: {u.get('id')}, Role: {u.get('role')})" for u in data_to_save["users"]]
        logger.info(f"Users being saved: {user_info}")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(USERS_CONFIG_PATH), exist_ok=True)

        # Log the file path
        logger.info(f"Saving users configuration to: {USERS_CONFIG_PATH}")

        with open(USERS_CONFIG_PATH, "w") as f:
            json.dump(data_to_save, f, indent=2)

        # Clear the cache to force a reload on next access
        cached_config = None
        last_modified_time = 0
        logger.info("Cleared configuration cache to force reload on next access")

        # Verify the file was written correctly
        if os.path.exists(USERS_CONFIG_PATH):
            file_size = os.path.getsize(USERS_CONFIG_PATH)
            logger.info(f"Successfully saved users configuration. File size: {file_size} bytes")

            # Double-check by reading the file back
            try:
                with open(USERS_CONFIG_PATH, "r") as f:
                    saved_data = json.load(f)
                    saved_users_count = len(saved_data.get("users", []))
                    logger.info(f"Verification: Read back {saved_users_count} users from saved file")
            except Exception as read_error:
                logger.warning(f"Could not verify saved file: {str(read_error)}")

        return True
    except Exception as e:
        logger.error(f"Error saving users configuration: {str(e)}")
        return False

# Login endpoint
@router.post("/login")
async def login(login_data: LoginRequest):
    try:
        config = load_users_config()
        users = config["users"]

        # Special case for SuperAdmin login
        if login_data.username.lower() == 'eagleai':
            # Find SuperAdmin user
            user = next((u for u in users if
                        u["username"] == login_data.username and
                        u["password"] == login_data.password and
                        u["role"] == "SuperAdmin"), None)

            if user:
                # Remove password from response
                user_data = {k: v for k, v in user.items() if k != "password"}
                return {
                    "success": True,
                    "data": {
                        "user": user_data
                    },
                    "message": "Login successful"
                }
            else:
                return {
                    "success": False,
                    "error": "Invalid SuperAdmin credentials"
                }

        # For Admin and Supervisor, check role
        if login_data.role:
            # Find user by username, password, and role
            user = next((u for u in users if
                        u["username"] == login_data.username and
                        u["password"] == login_data.password and
                        u["role"] == login_data.role), None)

            if user:
                # Remove password from response
                user_data = {k: v for k, v in user.items() if k != "password"}
                return {
                    "success": True,
                    "data": {
                        "user": user_data
                    },
                    "message": "Login successful"
                }
            else:
                # Check if username exists but role or password is wrong
                user_exists = next((u for u in users if u["username"] == login_data.username), None)

                if user_exists:
                    if user_exists["password"] != login_data.password:
                        return {
                            "success": False,
                            "error": "Invalid password"
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"User exists but is not a {login_data.role}"
                        }
                else:
                    return {
                        "success": False,
                        "error": "Invalid username or password"
                    }
        else:
            # If no role provided, find by username and password only
            user = next((u for u in users if
                        u["username"] == login_data.username and
                        u["password"] == login_data.password), None)

            if user:
                # Remove password from response
                user_data = {k: v for k, v in user.items() if k != "password"}
                return {
                    "success": True,
                    "data": {
                        "user": user_data
                    },
                    "message": "Login successful"
                }
            else:
                # Check if username exists but password is wrong
                user_exists = next((u for u in users if u["username"] == login_data.username), None)

                if user_exists:
                    return {
                        "success": False,
                        "error": "Invalid password"
                    }
                else:
                    return {
                        "success": False,
                        "error": "Invalid username or password"
                    }
    except Exception as e:
        logger.error(f"Error during login: {str(e)}")
        return {
            "success": False,
            "error": f"Login failed: {str(e)}"
        }

# Get all users
@router.get("")
async def get_users():
    try:
        config = load_users_config()
        # Remove passwords from response
        users = [{k: v for k, v in user.items() if k != "password"} for user in config["users"]]

        return {
            "success": True,
            "data": users,
            "message": "Users retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting users: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to retrieve users: {str(e)}"
        }

# Helper function to validate password
def validate_password(password, role):
    # Only validate for Admin and Supervisor roles
    if role in ["Admin", "Supervisor"]:
        # Password must be at least 8 characters long and include uppercase, lowercase, number, and special character
        pattern = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*]).{8,}$"
        if not re.match(pattern, password):
            return False
    return True

# Create a new user
@router.post("")
async def create_user(user: User):
    try:
        config = load_users_config()
        users = config["users"]

        # Check if username already exists
        if any(u["username"] == user.username for u in users):
            return {
                "success": False,
                "error": f"Username '{user.username}' already exists"
            }

        # Validate password for Admin and Supervisor roles
        if not validate_password(user.password, user.role):
            return {
                "success": False,
                "error": "Password must be at least 8 characters long and include uppercase, lowercase, number, and special character"
            }

        # Generate new ID
        new_id = 1
        if users:
            new_id = max(u["id"] for u in users) + 1

        # Create new user
        new_user = {
            "id": new_id,
            "username": user.username,
            "password": user.password,  # In production, this should be hashed
            "role": user.role,
            "permissions": user.permissions.dict(),
            "created_by": user.created_by
        }

        # Add to list
        users.append(new_user)

        # Save configuration
        if save_users_config(config):
            # Remove password from response
            user_data = {k: v for k, v in new_user.items() if k != "password"}
            return {
                "success": True,
                "data": user_data,
                "message": "User created successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to save configuration")
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to create user: {str(e)}"
        }

# Update user permissions
@router.put("/{user_id}/permissions")
async def update_user_permissions(user_id: int, permissions: dict, current_user_id: Optional[int] = None, current_user_role: Optional[str] = None):
    try:
        config = load_users_config()
        users = config["users"]

        # Find user by ID
        user_index = next((i for i, u in enumerate(users) if u["id"] == user_id), None)
        if user_index is None:
            logger.warning(f"User with ID {user_id} not found")
            return {
                "success": False,
                "error": f"User with ID {user_id} not found"
            }

        # Log the received permissions for debugging
        logger.info(f"Updating permissions for user {user_id}: {permissions}")

        # Get the user to update
        user_to_update = users[user_index]

        # Check if permissions is nested (has a 'permissions' key)
        if 'permissions' in permissions:
            # Extract the inner permissions object
            permissions = permissions['permissions']

        # If current_user_id is provided, validate that the user has permission to make these changes
        if current_user_id and current_user_role:
            # Find the current user
            current_user = next((u for u in users if u["id"] == current_user_id), None)
            if not current_user:
                logger.warning(f"Current user with ID {current_user_id} not found")
                return {
                    "success": False,
                    "error": "Current user not found"
                }

            # If current user is Admin, they can only grant permissions they have
            if current_user_role == 'Admin' and user_to_update["role"] == 'Supervisor':
                logger.info(f"Validating Admin permissions for {current_user['username']}")

                # Check each permission being granted
                for perm_id, is_granted in permissions.items():
                    # If trying to grant a permission
                    if is_granted:
                        # Check if Admin has this permission
                        if not current_user["permissions"].get(perm_id, False):
                            logger.warning(f"Admin {current_user['username']} attempted to grant permission {perm_id} they don't have")
                            return {
                                "success": False,
                                "error": f"You cannot grant permission '{perm_id}' that you don't have yourself"
                            }

                        # Special case: Admin can't grant manageUser permission to Supervisors
                        if perm_id == 'manageUser':
                            logger.warning(f"Admin {current_user['username']} attempted to grant manageUser permission to Supervisor {user_to_update['username']}")
                            return {
                                "success": False,
                                "error": "Admins cannot grant 'Manage User' permission to Supervisors"
                            }

        # Update permissions
        users[user_index]["permissions"] = permissions

        # If we're updating an Admin's permissions, also update their Supervisors' permissions
        if user_to_update["role"] == "Admin":
            logger.info(f"Admin {user_to_update['username']} permissions changed, updating their Supervisors")

            # Find all Supervisors created by this Admin
            supervisors_updated = 0
            for i, user in enumerate(users):
                if user["role"] == "Supervisor" and user.get("created_by") == user_to_update["username"]:
                    logger.info(f"Checking Supervisor {user['username']} permissions")

                    # Create a copy of the Supervisor's permissions
                    updated_supervisor_permissions = user["permissions"].copy()
                    permissions_changed = False

                    # For each permission, if Admin lost it, Supervisor should lose it too
                    for perm_id, supervisor_has_perm in user["permissions"].items():
                        if supervisor_has_perm and not permissions.get(perm_id, False):
                            logger.info(f"Removing permission {perm_id} from Supervisor {user['username']}")
                            updated_supervisor_permissions[perm_id] = False
                            permissions_changed = True

                    # Update the Supervisor's permissions if needed
                    if permissions_changed:
                        users[i]["permissions"] = updated_supervisor_permissions
                        supervisors_updated += 1
                        logger.info(f"Updated Supervisor {user['username']} permissions")

            if supervisors_updated > 0:
                logger.info(f"Updated permissions for {supervisors_updated} Supervisors")

        # Save configuration
        if save_users_config(config):
            # Remove password from response
            user_data = {k: v for k, v in users[user_index].items() if k != "password"}
            logger.info(f"Successfully updated permissions for user {user_to_update['username']}")
            return {
                "success": True,
                "data": user_data,
                "message": "Permissions updated successfully"
            }
        else:
            logger.error("Failed to save configuration after updating permissions")
            raise HTTPException(status_code=500, detail="Failed to save configuration")
    except Exception as e:
        logger.error(f"Error updating permissions: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to update permissions: {str(e)}"
        }

# Helper function to determine if a user can delete another user
def can_delete_user(deleter, target_user):
    # Cannot delete self
    if deleter["id"] == target_user["id"]:
        return False

    # SuperAdmin can delete any Admin or Supervisor, but not other SuperAdmins
    if deleter["role"] == "SuperAdmin":
        return target_user["role"] != "SuperAdmin"

    # Admin can only delete Supervisors they created
    if deleter["role"] == "Admin":
        return (
            target_user["role"] == "Supervisor" and
            target_user.get("created_by") == deleter["username"]
        )

    # Supervisors cannot delete anyone
    return False

# Delete a user
@router.delete("/{user_id}")
async def delete_user(user_id: int, current_user_id: Optional[int] = None, current_user_role: Optional[str] = None, current_user_username: Optional[str] = None):
    try:
        logger.info(f"Attempting to delete user with ID: {user_id}")
        logger.info(f"Current user info - ID: {current_user_id}, Role: {current_user_role}, Username: {current_user_username}")

        config = load_users_config()
        users = config["users"]

        # Log the current users before deletion
        logger.info(f"Current users before deletion: {[u['username'] for u in users]}")

        # Find user by ID
        user_index = next((i for i, u in enumerate(users) if u["id"] == user_id), None)
        if user_index is None:
            logger.warning(f"User with ID {user_id} not found")
            return {
                "success": False,
                "error": f"User with ID {user_id} not found"
            }

        user_to_delete = users[user_index]
        logger.info(f"Found user to delete: {user_to_delete['username']} (ID: {user_to_delete['id']}, Role: {user_to_delete['role']})")

        # Check if it's the last SuperAdmin
        if user_to_delete["role"] == "SuperAdmin" and len([u for u in users if u["role"] == "SuperAdmin"]) <= 1:
            logger.warning("Cannot delete the last SuperAdmin user")
            return {
                "success": False,
                "error": "Cannot delete the last SuperAdmin user"
            }

        # Apply deletion rules if current user info is provided
        if current_user_id and current_user_role and current_user_username:
            # Find the current user
            current_user = next((u for u in users if u["id"] == current_user_id), None)
            if not current_user:
                logger.warning(f"Current user with ID {current_user_id} not found")
                return {
                    "success": False,
                    "error": "Current user not found"
                }

            # Check if the current user can delete the target user
            if not can_delete_user(current_user, user_to_delete):
                logger.warning(f"User {current_user['username']} does not have permission to delete {user_to_delete['username']}")
                return {
                    "success": False,
                    "error": "You do not have permission to delete this user"
                }

        # Remove user
        deleted_user = users.pop(user_index)
        logger.info(f"Removed user {deleted_user['username']} from users list")

        # Log the users after deletion
        logger.info(f"Users after deletion: {[u['username'] for u in users]}")

        # Save configuration
        if save_users_config(config):
            logger.info(f"Successfully saved configuration after deleting user {deleted_user['username']}")
            return {
                "success": True,
                "message": f"User '{deleted_user['username']}' deleted successfully"
            }
        else:
            logger.error("Failed to save configuration after user deletion")
            raise HTTPException(status_code=500, detail="Failed to save configuration")
    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to delete user: {str(e)}"
        }

# Function to synchronize supervisor permissions with their admin's permissions
def synchronize_supervisor_permissions():
    try:
        logger.info("Synchronizing supervisor permissions with their admin's permissions")
        config = load_users_config()
        users = config["users"]

        # Create a dictionary of admins by username for quick lookup
        admins = {user["username"]: user for user in users if user["role"] == "Admin"}

        # Track supervisors that need updates
        supervisors_to_update = []

        # Check each supervisor
        for i, user in enumerate(users):
            if user["role"] == "Supervisor" and user.get("created_by") in admins:
                admin = admins[user["created_by"]]
                logger.info(f"Checking Supervisor {user['username']} created by Admin {admin['username']}")

                # Create a copy of the supervisor's permissions
                updated_permissions = user["permissions"].copy()
                permissions_changed = False

                # For each permission, if supervisor has it but admin doesn't, remove it
                for perm_id, has_perm in user["permissions"].items():
                    if has_perm and not admin["permissions"].get(perm_id, False):
                        logger.info(f"Removing permission {perm_id} from Supervisor {user['username']} (Admin {admin['username']} doesn't have it)")
                        updated_permissions[perm_id] = False
                        permissions_changed = True

                # Special case: manageUser should always be false for supervisors
                if updated_permissions.get("manageUser", False):
                    logger.info(f"Removing manageUser permission from Supervisor {user['username']}")
                    updated_permissions["manageUser"] = False
                    permissions_changed = True

                # If permissions changed, update the supervisor
                if permissions_changed:
                    users[i]["permissions"] = updated_permissions
                    supervisors_to_update.append(user["username"])

        # Save the configuration if any supervisors were updated
        if supervisors_to_update:
            logger.info(f"Updating permissions for supervisors: {supervisors_to_update}")
            if save_users_config(config):
                logger.info("Successfully synchronized supervisor permissions")
            else:
                logger.error("Failed to save configuration after synchronizing permissions")
        else:
            logger.info("No supervisor permissions needed to be synchronized")

    except Exception as e:
        logger.error(f"Error synchronizing supervisor permissions: {str(e)}")

# Run the synchronization when the module is loaded
synchronize_supervisor_permissions()

import React, { useState, useEffect } from 'react';
import './PerformanceMetrics.css';

function PerformanceMetrics() {
  const [cpu, setCpu] = useState(0);
  const [memory, setMemory] = useState(0);

  useEffect(() => {
    let interval;
    function updateMetrics() {
      // Electron's process.getCPUUsage() returns { percentCPUUsage }
      // process.memoryUsage().rss returns bytes
      const cpuUsage = process.getCPUUsage ? process.getCPUUsage().percentCPUUsage : 0;
      const memUsage = process.memoryUsage ? process.memoryUsage().rss : 0;
      setCpu(cpuUsage.toFixed(1));
      setMemory((memUsage / (1024 * 1024)).toFixed(1)); // MB
    }
    updateMetrics();
    // Update more frequently for dynamic display (every 500ms instead of 1000ms)
    interval = setInterval(updateMetrics, 500);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="performance-metrics">
      <div className="metric-item">
        <span className="metric-text">CPU: {cpu}%</span>
      </div>
      <div className="metric-item">
        <span className="metric-text">Memory: {memory} MB</span>
      </div>
    </div>
  );
}

export default PerformanceMetrics;
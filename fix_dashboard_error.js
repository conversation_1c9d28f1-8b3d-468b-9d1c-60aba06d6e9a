// Fix Dashboard.js "Collection not found" error
// Copy and paste this entire script into your browser console

(function() {
  console.log("Starting fix for Dashboard.js 'Collection not found' error...");
  
  // Step 1: Check for activeCollection in localStorage
  let foundActiveCollection = false;
  let activeCollectionKey = null;
  
  // Find the camera storage in localStorage
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key.includes('camera') || key.includes('vms')) {
      try {
        const value = localStorage.getItem(key);
        const parsed = JSON.parse(value);
        
        // Check if this object has activeCollection
        if (parsed && parsed.state && parsed.state.activeCollection) {
          console.log(`Found activeCollection in localStorage key: ${key}`);
          console.log(`Current activeCollection: ${parsed.state.activeCollection}`);
          foundActiveCollection = true;
          activeCollectionKey = key;
          
          // Check if the collections array contains this activeCollection
          const activeId = parsed.state.activeCollection;
          const collections = parsed.state.collections || [];
          const collectionExists = collections.some(c => c.id === activeId);
          
          if (!collectionExists) {
            console.log(`Active collection ID ${activeId} not found in collections array`);
            console.log("Setting activeCollection to null to fix the error");
            
            // Set activeCollection to null
            parsed.state.activeCollection = null;
            localStorage.setItem(key, JSON.stringify(parsed));
            console.log("Fixed: Set activeCollection to null");
          } else {
            console.log(`Active collection ID ${activeId} exists in collections array`);
          }
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }
  }
  
  if (!foundActiveCollection) {
    console.log("No activeCollection found in localStorage");
  }
  
  // Step 2: Check for any test_collection references
  let removedTestCollection = false;
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    
    if (value && value.toLowerCase().includes('test_collection')) {
      console.log(`Found test_collection reference in localStorage key: ${key}`);
      
      try {
        const parsed = JSON.parse(value);
        let modified = false;
        
        // Check for collections array
        if (parsed.state && parsed.state.collections) {
          const originalLength = parsed.state.collections.length;
          parsed.state.collections = parsed.state.collections.filter(collection => {
            return !(collection.name && collection.name.toLowerCase().includes('test_collection'));
          });
          
          if (parsed.state.collections.length < originalLength) {
            console.log(`Removed ${originalLength - parsed.state.collections.length} test_collection entries`);
            modified = true;
          }
        }
        
        // Check for cameras array
        if (parsed.state && parsed.state.cameras) {
          const originalLength = parsed.state.cameras.length;
          parsed.state.cameras = parsed.state.cameras.filter(camera => {
            return !(
              (camera.name && camera.name.toLowerCase().includes('test_collection')) ||
              (camera.ip === '************' || camera.ip === '************') ||
              (camera.streamUrl && (
                camera.streamUrl.includes('************') || 
                camera.streamUrl.includes('************')
              ))
            );
          });
          
          if (parsed.state.cameras.length < originalLength) {
            console.log(`Removed ${originalLength - parsed.state.cameras.length} test_collection cameras`);
            modified = true;
          }
        }
        
        // Check for cameraJson
        if (parsed.state && parsed.state.cameraJson) {
          const keys = Object.keys(parsed.state.cameraJson);
          for (const key of keys) {
            if (key.toLowerCase().includes('test_collection')) {
              delete parsed.state.cameraJson[key];
              console.log(`Removed test_collection from cameraJson: ${key}`);
              modified = true;
            }
          }
        }
        
        if (modified) {
          localStorage.setItem(key, JSON.stringify(parsed));
          removedTestCollection = true;
        }
      } catch (e) {
        console.log(`Could not parse as JSON: ${key}`);
      }
    }
  }
  
  // Step 3: Force a reload if we made changes
  if (foundActiveCollection || removedTestCollection) {
    console.log("Changes made to localStorage. Reloading page to apply changes...");
    alert("Fixed 'Collection not found' error. The page will now reload.");
    window.location.reload();
  } else {
    console.log("No changes made to localStorage.");
    alert("No issues found that would cause 'Collection not found' error. Try restarting the application.");
  }
})();

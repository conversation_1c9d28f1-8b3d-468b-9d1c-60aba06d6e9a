.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.map-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.sidebar-btn-sub {
  display: flex;
  align-items: flex-start; /* Align to top for wrapped text */
  padding: 8px 12px; /* Reduced padding for better fit */
  width: calc(100% - 8px); /* Account for margins and prevent overflow */
  max-width: calc(100% - 8px);
  border: none;
  background: transparent;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.3s;
  box-sizing: border-box; /* Include padding in width calculation */
  word-wrap: break-word; /* Break long words */
  overflow-wrap: break-word; /* Modern word breaking */
  white-space: normal; /* Allow text wrapping */
  text-align: left; /* Align text to left */
  margin: 2px 0; /* Add small margin for spacing */
  border-radius: 6px; /* Add border radius for consistency */
}

.sidebar-btn-sub:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-btn-sub.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.sidebar-icon {
  margin-right: 8px;
  font-size: 1.1em;
  flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Map selector container */
.map-selector {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow: visible;
  box-sizing: border-box;
}
.mjpeg-player {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.mjpeg-stream {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.mjpeg-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ffffff;
  background-color: #1a1a1a;
  gap: 0.5rem;
}

.mjpeg-loading span {
  font-size: 0.875rem;
  color: #d1d5db;
}

.mjpeg-loading small {
  font-size: 0.75rem;
  color: #9ca3af;
}

.mjpeg-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ffffff;
  background-color: #1a1a1a;
  gap: 0.5rem;
  padding: 1rem;
  text-align: center;
}

.mjpeg-error span {
  font-size: 0.875rem;
  color: #ef4444;
}

.retry-button {
  margin-top: 0.5rem;
  padding: 0.25rem 0.75rem;
  background-color: #FFFFFF;
  color: black;
  border: 1px solid #000000;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: #F8F9FA;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #374151;
  border-top: 2px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mjpeg-loading,
  .mjpeg-error {
    gap: 0.25rem;
  }

  .mjpeg-loading span,
  .mjpeg-error span {
    font-size: 0.75rem;
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
  }

  .retry-button {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
  }
}

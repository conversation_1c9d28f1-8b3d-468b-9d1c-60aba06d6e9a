import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ArchivePlaybackContent from './ArchivePlaybackContent';

// Mock the stores and services
jest.mock('../../store/cameraStore', () => ({
  useCameraStore: () => ({
    cameras: [
      { id: 'cam1', name: 'Camera 1', collection: 'Eagle', ip: '*************' },
      { id: 'cam2', name: 'Camera 2', collection: 'Eagle', ip: '*************' },
      { id: 'cam3', name: 'Camera 3', collection: 'Eagle', ip: '*************' }
    ]
  })
}));

jest.mock('../../store/archiveStore', () => ({
  useArchiveStore: () => ({
    recordings: [
      {
        filename: '2025-01-15_00-00-00.mp4',
        stream_id: 'Eagle_*************',
        timestamp: '2025-01-15T00:00:00Z',
        size_bytes: 1048576,
        size_mb: 1
      },
      {
        filename: '2025-01-14_00-00-00.mp4',
        stream_id: 'Eagle_*************',
        timestamp: '2025-01-14T00:00:00Z',
        size_bytes: 2097152,
        size_mb: 2
      }
    ],
    availableStreams: [
      {
        stream_id: 'Eagle_*************',
        collection_name: 'Eagle',
        camera_ip: '*************',
        recording_count: 5
      },
      {
        stream_id: 'Eagle_*************',
        collection_name: 'Eagle',
        camera_ip: '*************',
        recording_count: 3
      }
    ],
    isLoading: false,
    error: null,
    filters: {
      dateRange: 'today',
      streamId: 'all',
      sortBy: 'newest'
    },
    selectedStreamId: null,
    loadRecordings: jest.fn(),
    loadAvailableStreams: jest.fn(),
    setFilters: jest.fn(),
    getFilteredRecordings: jest.fn(() => [
      {
        filename: '2025-01-15_00-00-00.mp4',
        stream_id: 'Eagle_*************',
        timestamp: '2025-01-15T00:00:00Z',
        size_bytes: 1048576,
        size_mb: 1
      }
    ]),
    clearError: jest.fn()
  })
}));

jest.mock('../../services/archiveApi', () => ({
  formatRecordingDate: jest.fn((timestamp) => new Date(timestamp).toLocaleString()),
  formatFileSize: jest.fn((bytes) => `${Math.round(bytes / 1024)} KB`),
  getRecordingStreamUrl: jest.fn((streamId, filename) => `http://localhost:8000/api/archive/stream/${streamId}/${filename}`),
  parseStreamId: jest.fn((streamId) => {
    const parts = streamId.split('_');
    return {
      collectionName: parts[0],
      cameraIp: parts[1]
    };
  })
}));

describe('ArchivePlaybackContent Component', () => {
  const mockOnSelectRecording = jest.fn();

  beforeEach(() => {
    mockOnSelectRecording.mockClear();
    jest.clearAllMocks();
  });

  describe('Filter Interface', () => {
    test('renders all filter cards correctly', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      expect(screen.getByText('Archive Playback')).toBeInTheDocument();
      expect(screen.getByText('Date Range')).toBeInTheDocument();
      expect(screen.getByText('Camera')).toBeInTheDocument();
      expect(screen.getByText('Sort By')).toBeInTheDocument();
    });

    test('renders refresh button', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toBeInTheDocument();
    });

    test('date range filter works correctly', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const dateRangeFilter = screen.getByText('Date Range').closest('.filter-card');
      const selectButton = dateRangeFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        expect(screen.getByText('Yesterday')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText('Yesterday'));
      
      // Should show active state
      expect(dateRangeFilter).toHaveClass('active');
    });

    test('camera filter includes all cameras', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const cameraFilter = screen.getByText('Camera').closest('.filter-card');
      const selectButton = cameraFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        expect(screen.getByText('All Cameras')).toBeInTheDocument();
        expect(screen.getByText('Camera 1')).toBeInTheDocument();
        expect(screen.getByText('Camera 2')).toBeInTheDocument();
        expect(screen.getByText('Camera 3')).toBeInTheDocument();
      });
    });

    test('search filter works correctly', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const searchInput = screen.getByPlaceholderText('Search recordings...');
      
      fireEvent.change(searchInput, { target: { value: 'Camera 1' } });
      
      expect(searchInput.value).toBe('Camera 1');
      
      // Should show active state
      const searchFilter = searchInput.closest('.filter-card');
      expect(searchFilter).toHaveClass('active');
    });

    test('recording type filter has correct options', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const recordingTypeFilter = screen.getByText('Recording Type').closest('.filter-card');
      const selectButton = recordingTypeFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        expect(screen.getByText('All Types')).toBeInTheDocument();
        expect(screen.getByText('Manual')).toBeInTheDocument();
        expect(screen.getByText('Scheduled')).toBeInTheDocument();
        expect(screen.getByText('Motion Triggered')).toBeInTheDocument();
        expect(screen.getByText('Event Triggered')).toBeInTheDocument();
      });
    });

    test('duration filter has correct options', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const durationFilter = screen.getByText('Duration').closest('.filter-card');
      const selectButton = durationFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        expect(screen.getByText('Any Duration')).toBeInTheDocument();
        expect(screen.getByText('< 5 minutes')).toBeInTheDocument();
        expect(screen.getByText('5-30 minutes')).toBeInTheDocument();
        expect(screen.getByText('> 30 minutes')).toBeInTheDocument();
      });
    });

    test('sort by filter has correct options', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const sortByFilter = screen.getByText('Sort By').closest('.filter-card');
      const selectButton = sortByFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        expect(screen.getByText('Newest First')).toBeInTheDocument();
        expect(screen.getByText('Oldest First')).toBeInTheDocument();
        expect(screen.getByText('Camera Name')).toBeInTheDocument();
      });
    });
  });

  describe('Clear Filters Functionality', () => {
    test('shows clear all filters button when filters are applied', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      // Apply a filter
      const cameraFilter = screen.getByText('Camera').closest('.filter-card');
      const selectButton = cameraFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        fireEvent.click(screen.getByText('Camera 1'));
      });
      
      // Should show clear filters button
      expect(screen.getByText('Clear All Filters')).toBeInTheDocument();
    });

    test('clear all filters button resets all filters', async () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      // Apply multiple filters
      const searchInput = screen.getByPlaceholderText('Search recordings...');
      fireEvent.change(searchInput, { target: { value: 'test' } });
      
      const cameraFilter = screen.getByText('Camera').closest('.filter-card');
      const selectButton = cameraFilter.querySelector('.filter-select-button');
      
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        fireEvent.click(screen.getByText('Camera 1'));
      });
      
      // Click clear all filters
      const clearButton = screen.getByText('Clear All Filters');
      fireEvent.click(clearButton);
      
      // Check that filters are reset
      expect(searchInput.value).toBe('');
      expect(screen.getByText('All Cameras')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels for filter controls', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);
      
      const refreshButton = screen.getByRole('button', { name: /refresh recordings/i });
      expect(refreshButton).toHaveAttribute('aria-label', 'Refresh recordings');
      
      const searchInput = screen.getByRole('textbox', { name: /search filter input/i });
      expect(searchInput).toBeInTheDocument();
    });
  });

  describe('Recording Display', () => {
    test('displays recordings when available', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      // Should show recording cards
      expect(screen.getByText('Eagle (*************)')).toBeInTheDocument();
      expect(screen.getByText('1 KB')).toBeInTheDocument(); // File size
    });

    test('shows no recordings message when empty', () => {
      // Mock empty recordings
      const { useArchiveStore } = require('../../store/archiveStore');
      useArchiveStore.mockReturnValue({
        ...useArchiveStore(),
        getFilteredRecordings: jest.fn(() => [])
      });

      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      expect(screen.getByText('No archived recordings found.')).toBeInTheDocument();
    });

    test('play button calls onSelectRecording with correct data', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      const playButton = screen.getByRole('button', { name: /play/i });
      fireEvent.click(playButton);

      expect(mockOnSelectRecording).toHaveBeenCalledWith(
        expect.objectContaining({
          id: '2025-01-15_00-00-00.mp4',
          streamId: 'Eagle_*************',
          filename: '2025-01-15_00-00-00.mp4',
          streamUrl: expect.stringContaining('http://localhost:8000/api/archive/stream')
        })
      );
    });
  });

  describe('Video Player', () => {
    const mockRecording = {
      id: '2025-01-15_00-00-00.mp4',
      streamId: 'Eagle_*************',
      filename: '2025-01-15_00-00-00.mp4',
      streamUrl: 'http://localhost:8000/api/archive/stream/Eagle_*************/2025-01-15_00-00-00.mp4',
      timestamp: '2025-01-15T00:00:00Z',
      size: 1
    };

    test('shows video player when recording is selected', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} selectedRecordingId={mockRecording} />);

      expect(screen.getByText('Archive Playback')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back to recordings/i })).toBeInTheDocument();

      const video = screen.getByLabelText('Archive recording video player');
      expect(video).toBeInTheDocument();
      expect(video).toHaveAttribute('src', mockRecording.streamUrl);
    });

    test('back button returns to recordings list', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} selectedRecordingId={mockRecording} />);

      const backButton = screen.getByRole('button', { name: /back to recordings/i });
      fireEvent.click(backButton);

      expect(mockOnSelectRecording).toHaveBeenCalledWith(null);
    });

    test('displays recording metadata', () => {
      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} selectedRecordingId={mockRecording} />);

      expect(screen.getByText('Size: 1 MB')).toBeInTheDocument();
    });
  });

  describe('Loading and Error States', () => {
    test('shows loading state', () => {
      const { useArchiveStore } = require('../../store/archiveStore');
      useArchiveStore.mockReturnValue({
        ...useArchiveStore(),
        isLoading: true
      });

      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      expect(screen.getByText('Loading recordings...')).toBeInTheDocument();
      expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument(); // Loading spinner
    });

    test('shows error state with retry button', () => {
      const { useArchiveStore } = require('../../store/archiveStore');
      const mockStore = {
        ...useArchiveStore(),
        isLoading: false,
        error: 'Failed to load recordings'
      };
      useArchiveStore.mockReturnValue(mockStore);

      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      expect(screen.getByText('Failed to load recordings')).toBeInTheDocument();

      const retryButton = screen.getByRole('button', { name: /try again/i });
      expect(retryButton).toBeInTheDocument();

      fireEvent.click(retryButton);
      expect(mockStore.clearError).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    test('loads available streams on mount', () => {
      const { useArchiveStore } = require('../../store/archiveStore');
      const mockStore = useArchiveStore();

      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      expect(mockStore.loadAvailableStreams).toHaveBeenCalled();
    });

    test('loads recordings when camera filter changes', () => {
      const { useArchiveStore } = require('../../store/archiveStore');
      const mockStore = useArchiveStore();

      render(<ArchivePlaybackContent onSelectRecording={mockOnSelectRecording} />);

      // Simulate camera filter change
      const cameraFilter = screen.getByText('Camera').closest('.filter-card');
      const selectButton = cameraFilter?.querySelector('.filter-select-button');

      if (selectButton) {
        fireEvent.click(selectButton);
        // This would trigger loadRecordings in the real component
        expect(mockStore.setFilters).toHaveBeenCalled();
      }
    });
  });
});

/* Recording Status Indicator Styles */

.recording-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.recording-status-indicator.size-small {
  padding: 2px 6px;
  font-size: 11px;
  gap: 4px;
}

.recording-status-indicator.size-medium {
  padding: 4px 8px;
  font-size: 12px;
  gap: 6px;
}

.recording-status-indicator.size-large {
  padding: 6px 12px;
  font-size: 14px;
  gap: 8px;
}

/* Status Dot */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.size-small .status-dot {
  width: 6px;
  height: 6px;
}

.size-large .status-dot {
  width: 10px;
  height: 10px;
}

/* Pulse Animation for Active Recording */
.status-dot.pulse {
  animation: recordingPulse 2s infinite;
  box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
}

@keyframes recordingPulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(255, 68, 68, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0);
  }
}

/* Status Label */
.status-label {
  color: #ffffff;
  white-space: nowrap;
}

/* Status Variants */
.recording-status-indicator.recording-active {
  background: rgba(255, 68, 68, 0.1);
  border-color: rgba(255, 68, 68, 0.3);
}

.recording-status-indicator.recording-active .status-label {
  color: #ff6b6b;
}

.recording-status-indicator.recording-stopped {
  background: rgba(102, 102, 102, 0.1);
  border-color: rgba(102, 102, 102, 0.3);
}

.recording-status-indicator.recording-stopped .status-label {
  color: #999999;
}

.recording-status-indicator.recording-stale {
  background: rgba(255, 153, 0, 0.1);
  border-color: rgba(255, 153, 0, 0.3);
}

.recording-status-indicator.recording-stale .status-label {
  color: #ff9900;
}

.recording-status-indicator.recording-unknown {
  background: rgba(136, 136, 136, 0.1);
  border-color: rgba(136, 136, 136, 0.3);
}

.recording-status-indicator.recording-unknown .status-label {
  color: #888888;
}

/* Overall Recording Status */
.overall-recording-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
  border: 1px solid rgba(122, 50, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.overall-recording-status.active {
  border-color: rgba(255, 68, 68, 0.5);
  background: linear-gradient(135deg, #2d2d2d, #3d2d2d);
}

.overall-recording-status.inactive {
  border-color: rgba(102, 102, 102, 0.3);
}

.overall-recording-status.loading {
  border-color: rgba(122, 50, 255, 0.5);
}

.overall-recording-status.error {
  border-color: rgba(255, 107, 107, 0.5);
  background: linear-gradient(135deg, #3d2d2d, #3d2d2d);
}

.overall-recording-status .status-dot {
  width: 10px;
  height: 10px;
}

.overall-recording-status.active .status-dot {
  background-color: #ff4444;
}

.overall-recording-status.inactive .status-dot {
  background-color: #666666;
}

.overall-recording-status.loading .status-dot {
  background-color: #7A32FF;
}

.overall-recording-status.error .status-dot {
  background-color: #ff6b6b;
}

.loading-dot {
  animation: loadingPulse 1.5s infinite;
}

@keyframes loadingPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.error-dot {
  animation: errorBlink 2s infinite;
}

@keyframes errorBlink {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.3;
  }
}

.status-text {
  color: #ffffff;
  font-weight: 500;
}

.status-detail {
  color: #cccccc;
  font-size: 12px;
  font-weight: 400;
}

/* Camera Recording Status */
.camera-recording-status {
  background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
  border: 1px solid rgba(122, 50, 255, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.camera-recording-status h4 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.camera-recording-status.empty {
  text-align: center;
  padding: 24px;
  color: #888888;
}

.camera-status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.camera-status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.camera-status-item:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(122, 50, 255, 0.3);
}

.camera-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.camera-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 14px;
}

.camera-ip {
  color: #cccccc;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recording-status-indicator {
    padding: 3px 6px;
    font-size: 11px;
    gap: 4px;
  }
  
  .overall-recording-status {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .camera-status-item {
    padding: 6px 10px;
  }
  
  .camera-name {
    font-size: 13px;
  }
  
  .camera-ip {
    font-size: 11px;
  }
}

/* Accessibility */
.recording-status-indicator:focus {
  outline: 2px solid rgba(122, 50, 255, 0.5);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .recording-status-indicator {
    border-width: 2px;
  }
  
  .status-dot {
    border: 1px solid #ffffff;
  }
}

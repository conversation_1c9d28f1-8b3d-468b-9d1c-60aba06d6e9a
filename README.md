# Video Management System (VMS)

## Camera Stream Configuration

This application allows you to configure and view RTSP camera streams. The camera configuration is stored in a JSON file called `camera_configuration.json` in the `backend/data` directory of the project.

### Camera Configuration Format

The camera configuration file uses the following format:

```json
{
  "collection_name": {
    "camera_ip": "rtsp_url"
  }
}
```

For example:

```json
{
  "karnal": {
    "************": "rtsp://service:Krnl$001@************:554"
  }
}
```

### How to Use

1. Make sure your camera configuration is properly set up in the `camera_configuration.json` file.
2. Start the backend server:
   ```
   cd backend
   python main.py
   ```
3. Start the frontend application:
   ```
   npm start
   ```
4. In the application, click on "RTSP Streams" in the sidebar to view all configured camera streams.

### How It Works

1. The backend reads the camera configuration from `camera_configuration.json`
2. When a stream is requested, the backend converts the RTSP stream to MJPEG format
3. The frontend displays the MJPEG stream directly in the browser
4. No dummy files are used - the actual camera stream is displayed

### Adding New Cameras

You can add new cameras by:

1. Manually editing the `camera_configuration.json` file
2. Using the application's UI to add new cameras (Configuration tab)

### Requirements

- The RTSP cameras must be accessible from the server running the backend
- A modern browser that supports MJPEG streaming (Chrome, Firefox, Edge, etc.)
- Python backend with OpenCV for RTSP stream processing

### Troubleshooting

If you encounter issues with the camera streams:

1. Make sure the RTSP URLs are correct and accessible from your network
2. Verify that the backend server is running and accessible
3. Check the browser console and backend logs for any error messages
4. Try accessing the RTSP stream directly using VLC or another media player to verify it's working
5. Make sure your firewall allows the necessary connections

### Advanced Configuration

You can modify the OpenCV parameters in `backend/main.py` to adjust the streaming quality, format, and other options. The current configuration is optimized for low-latency streaming with good compatibility.
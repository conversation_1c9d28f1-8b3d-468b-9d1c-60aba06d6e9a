import React, { useState, useEffect } from 'react';
import './EventsContent.css';
import DetectionRuleSet from './DetectionRuleSet';
import EventStatistics from './EventStatistics';
import RulesOnCamera from './RulesOnCamera';
import FilterCard from './FilterCard';
import { useUserStore } from '../../store/userStore';

function EventsContent({ selectedMenu }) {
  const [activeContent, setActiveContent] = useState('search');
  const currentUser = useUserStore(state => state.currentUser);

  // Check if user is SuperAdmin
  const isSuperAdmin = currentUser && currentUser.role === 'SuperAdmin';

  // Filter states
  const [filters, setFilters] = useState({
    timeRange: 'today',
    rule: '',
    priority: 'all',
    acknowledge: 'all',
    location: '',
    camera: ''
  });

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    // Implement refresh logic here
    console.log('Refreshing events...');
  };

  useEffect(() => {
    // Update active content based on selected menu
    if (selectedMenu === 'detection-rule-set') {
      // Only allow SuperAdmin to access detection-rule-set
      if (isSuperAdmin) {
        setActiveContent('detection-rule-set');
      } else {
        // Redirect non-SuperAdmin users to search
        setActiveContent('search');
      }
    } else if (selectedMenu === 'events-statistics') {
      setActiveContent('events-statistics');
    } else if (selectedMenu === 'rules-on-camera') {
      setActiveContent('rules-on-camera');
    } else {
      setActiveContent('search');
    }
  }, [selectedMenu, isSuperAdmin]);

  const renderContent = () => {
    switch (activeContent) {
      case 'detection-rule-set':
        // Double-check that only SuperAdmin can access this component
        return isSuperAdmin ? <DetectionRuleSet /> : (
          <div className="unauthorized-access">
            <h3>Unauthorized Access</h3>
            <p>You do not have permission to access Detection Rule Set.</p>
          </div>
        );
      case 'events-statistics':
        return <EventStatistics />;
      case 'rules-on-camera':
        return <RulesOnCamera />;
      default:
        return (
          <>
            <div className="events-filters-container">
              <div className="events-filters-header">
                <div className="events-title">
                  <span className="events-icon">🗂️</span>
                  <h2>Search Events</h2>
                </div>
                <button
                  className="refresh-button"
                  onClick={handleRefresh}
                  aria-label="Refresh events"
                >
                  <span className="refresh-icon">⟳</span>
                  Refresh
                </button>
              </div>

              <div className="events-filters-grid">
                <FilterCard
                  title="Time Range"
                  type="select"
                  value={filters.timeRange}
                  onChange={(value) => handleFilterChange('timeRange', value)}
                  options={[
                    { value: 'today', label: 'Today' },
                    { value: 'yesterday', label: 'Yesterday' },
                    { value: 'week', label: 'This Week' },
                    { value: 'month', label: 'This Month' },
                    { value: 'custom', label: 'Custom Range' }
                  ]}
                  isActive={filters.timeRange !== 'today'}
                />

                <FilterCard
                  title="Detection Rule"
                  type="select"
                  value={filters.rule}
                  onChange={(value) => handleFilterChange('rule', value)}
                  options={[
                    { value: '', label: 'All Rules' },
                    { value: 'motion', label: 'Motion Detection' },
                    { value: 'intrusion', label: 'Intrusion Detection' },
                    { value: 'object', label: 'Object Detection' },
                    { value: 'face', label: 'Face Recognition' }
                  ]}
                  isActive={filters.rule !== ''}
                />

                <FilterCard
                  title="Priority Level"
                  type="select"
                  value={filters.priority}
                  onChange={(value) => handleFilterChange('priority', value)}
                  options={[
                    { value: 'all', label: 'All Priorities' },
                    { value: 'critical', label: 'Critical' },
                    { value: 'high', label: 'High' },
                    { value: 'medium', label: 'Medium' },
                    { value: 'low', label: 'Low' }
                  ]}
                  isActive={filters.priority !== 'all'}
                />

                <FilterCard
                  title="Acknowledgment"
                  type="select"
                  value={filters.acknowledge}
                  onChange={(value) => handleFilterChange('acknowledge', value)}
                  options={[
                    { value: 'all', label: 'All Events' },
                    { value: 'acknowledged', label: 'Acknowledged' },
                    { value: 'unacknowledged', label: 'Unacknowledged' }
                  ]}
                  isActive={filters.acknowledge !== 'all'}
                />

                <FilterCard
                  title="Location"
                  type="select"
                  value={filters.location}
                  onChange={(value) => handleFilterChange('location', value)}
                  options={[
                    { value: '', label: 'All Locations' },
                    { value: 'entrance', label: 'Main Entrance' },
                    { value: 'parking', label: 'Parking Area' },
                    { value: 'lobby', label: 'Lobby' },
                    { value: 'corridor', label: 'Corridor' }
                  ]}
                  isActive={filters.location !== ''}
                />

                <FilterCard
                  title="Camera"
                  type="input"
                  value={filters.camera}
                  onChange={(value) => handleFilterChange('camera', value)}
                  placeholder="Search camera..."
                  isActive={filters.camera !== ''}
                />
              </div>
            </div>

            {/* Default events content */}
            <div className="events-placeholder">
              <p>Select an event from the sidebar to view details</p>
            </div>
          </>
        );
    }
  };

  return (
    <div className="events-content">
      {renderContent()}
    </div>
  );
}

export default EventsContent;
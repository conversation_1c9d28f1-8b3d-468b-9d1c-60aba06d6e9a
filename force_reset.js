// Force Reset Script for VMS Application
// This script completely resets the application state and forces a reload from the backend

(function() {
  console.log("Starting FORCE RESET of VMS application state...");
  
  // Step 1: Clear all localStorage items related to the application
  console.log("Clearing all localStorage items...");
  const keysToRemove = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    // Add any key that might be related to the application
    if (key.includes('camera') || 
        key.includes('collection') || 
        key.includes('vms') || 
        key.includes('storage') || 
        key.includes('state') ||
        key.includes('persist')) {
      keysToRemove.push(key);
    }
  }
  
  // Remove all identified keys
  keysToRemove.forEach(key => {
    console.log(`Removing localStorage key: ${key}`);
    localStorage.removeItem(key);
  });
  
  console.log(`Removed ${keysToRemove.length} localStorage items`);
  
  // Step 2: Try to access the application's store if available
  try {
    // This is a common pattern for accessing Zustand stores in the global scope
    if (window.__ZUSTAND_DEVTOOLS_EXTENSION__) {
      console.log("Attempting to reset Zustand stores...");
      const stores = window.__ZUSTAND_DEVTOOLS_EXTENSION__.stores;
      if (stores && stores.length) {
        stores.forEach(store => {
          if (store && store.setState) {
            // Reset to initial state
            store.setState({}, true);
            console.log("Reset a Zustand store to empty state");
          }
        });
      }
    }
    
    // Try to find any global store objects
    if (window.useCameraStore && window.useCameraStore.getState) {
      console.log("Found camera store, resetting...");
      window.useCameraStore.setState({
        cameras: [],
        bookmarks: [],
        collections: [],
        activeCollection: null,
        currentLayout: 'grid',
        cameraJson: {}
      });
    }
  } catch (e) {
    console.log("Error trying to reset stores:", e);
  }
  
  // Step 3: Force reload the application
  console.log("Forcing application reload...");
  
  // Add a flag to indicate we're coming from a force reset
  sessionStorage.setItem('vms_force_reset', 'true');
  
  // Force a hard reload (bypass cache)
  window.location.reload(true);
})();

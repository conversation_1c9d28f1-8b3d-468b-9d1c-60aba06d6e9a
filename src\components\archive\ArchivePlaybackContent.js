// Enhanced Archive Playback with modern VMS-compatible UI
import React, { useState, useEffect } from 'react';
import { useArchiveStore } from '../../store/archiveStore';
import archiveApi from '../../services/archiveApi';
import {
  RefreshCw,
  RotateCcw,
  Video,
  Play,
  Trash2,
  Clock,
  HardDrive,
  Calendar,
  Filter,
  Search
} from 'lucide-react';
import './ArchivePlaybackContent.css';

// Helper function to format date
const formatDate = (timestamp) => {
  if (!timestamp) return 'Unknown';
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

const ArchivePlaybackContent = ({ onSelectRecording, selectedRecordingId }) => {
  const {
    recordings,
    availableStreams,
    isLoading,
    error,
    filters,
    selectedStreamId,
    loadRecordings,
    loadAvailableStreams,
    setFilters,
    getFilteredRecordings,
    clearError,
    startStatusPolling,
    stopStatusPolling,
    restartRecordings
  } = useArchiveStore();

  const [filteredRecordings, setFilteredRecordings] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);

  // Load available streams and start status polling on component mount
  useEffect(() => {
    loadAvailableStreams();
    startStatusPolling();
    return () => {
      stopStatusPolling();
    };
  }, [loadAvailableStreams, startStatusPolling, stopStatusPolling]);

  // Update filtered recordings when recordings or filters change
  useEffect(() => {
    const filtered = getFilteredRecordings();
    setFilteredRecordings(filtered);
  }, [recordings, filters, getFilteredRecordings]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadRecordings(true); // Force refresh
      await loadAvailableStreams();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleRestart = async () => {
    setIsRestarting(true);
    try {
      await restartRecordings();
      await loadRecordings(true);
    } finally {
      setIsRestarting(false);
    }
  };

  const handlePlayRecording = (recording) => {
    if (onSelectRecording) {
      onSelectRecording(recording);
    }
  };

  return (
    <div className="archive-playback-content">
      <div className="recordings-container">
        {/* Archive Header */}
        <div className="archive-filters-container">
          <div className="archive-filters-header">
            <div className="archive-title">
              <h2>Archive Recordings</h2>
              <p className="archive-subtitle">
                Browse and playback recorded video content from all cameras
              </p>
            </div>
            <div className="action-buttons">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="refresh-button"
              >
                <RefreshCw className={`refresh-icon ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </button>
              <button
                onClick={handleRestart}
                disabled={isRestarting}
                className="restart-button"
              >
                <RotateCcw className={`restart-icon ${isRestarting ? 'animate-spin' : ''}`} />
                {isRestarting ? 'Restarting...' : 'Restart Recording'}
              </button>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="archive-content-area">
          {/* Loading State */}
          {isLoading && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <h3>Loading Archive</h3>
              <p>Fetching recordings from all cameras...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="error-container">
              <div className="error-icon">⚠️</div>
              <h3>Error Loading Recordings</h3>
              <p className="error-message">{error}</p>
              {error.includes('backend is not available') && (
                <div className="backend-unavailable-info">
                  <p className="info-text">
                    The archive backend is currently not running. To access recordings:
                  </p>
                  <ul className="info-list">
                    <li>Start the Python backend server</li>
                    <li>Ensure the archive recording service is running</li>
                    <li>Check that recordings exist in the ./recordings/ directory</li>
                  </ul>
                </div>
              )}
              <button onClick={handleRefresh} className="retry-button">
                Try Again
              </button>
            </div>
          )}

          {/* Recordings Grid */}
          {!isLoading && !error && (
            <div className="recordings-grid">
              {filteredRecordings.length === 0 ? (
                <div className="no-recordings">
                  <div className="no-recordings-icon">📁</div>
                  <h3>No archived recordings found</h3>
                  <p>
                    {selectedStreamId
                      ? "No recordings available for the selected camera."
                      : "No recordings are currently available from any camera."
                    }
                  </p>
                  <p className="hint">
                    Recordings will appear here once the cameras start recording or when existing recordings are detected.
                  </p>
                  {selectedStreamId && (
                    <button
                      onClick={() => setFilters({ dateRange: 'all', sortBy: 'newest' })}
                      className="clear-filter-button"
                    >
                      Show All Recordings
                    </button>
                  )}
                </div>
              ) : (
                filteredRecordings.map(recording => (
                  <div
                    key={recording.filename}
                    className="recording-card"
                    onClick={() => handlePlayRecording(recording)}
                  >
                    {/* Thumbnail */}
                    <div className="recording-thumbnail">
                      <div className="thumbnail-placeholder">
                        <Video className="camera-icon" />
                      </div>
                    </div>

                    {/* Recording Info */}
                    <div className="recording-info">
                      <h4 className="recording-camera">
                        {archiveApi.parseStreamId(recording.stream_id).collectionName}
                      </h4>
                      <p className="recording-time">
                        {formatDate(recording.timestamp)}
                      </p>
                      <p className="recording-duration">
                        {archiveApi.formatRecordingDuration(recording)}
                      </p>
                      <p className="recording-size">
                        {archiveApi.formatFileSize(recording.size_bytes)}
                      </p>
                      <p className="recording-ip">
                        {archiveApi.parseStreamId(recording.stream_id).cameraIp}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="recording-actions">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlayRecording(recording);
                        }}
                        className="play-button"
                      >
                        <Play size={16} />
                        Play
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle delete functionality
                          console.log('Delete recording:', recording.filename);
                        }}
                        className="delete-button"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(ArchivePlaybackContent);

// Enhanced Archive Playback with modern VMS-compatible UI
import React, { useState, useEffect, useRef } from 'react';
import { useArchiveStore } from '../../store/archiveStore';
import archiveApi from '../../services/archiveApi';
import {
  RefreshCw,
  RotateCcw,
  Video,
  Play,
  Pause,
  Trash2,
  Clock,
  HardDrive,
  Calendar,
  Filter,
  Search,
  Grid,
  List,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  Download
} from 'lucide-react';
import './ArchivePlaybackContent.css';

// Helper function to format date
const formatDate = (timestamp) => {
  if (!timestamp) return 'Unknown';
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

// Helper function to format time in HH:MM:SS format
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00:00';
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const ArchivePlaybackContent = ({ onSelectRecording, selectedRecordingId }) => {
  const {
    recordings,
    availableStreams,
    isLoading,
    error,
    filters,
    selectedStreamId,
    loadRecordings,
    loadAvailableStreams,
    setFilters,
    getFilteredRecordings,
    clearError,
    startStatusPolling,
    stopStatusPolling,
    restartRecordings
  } = useArchiveStore();

  // Enhanced state management
  const [filteredRecordings, setFilteredRecordings] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);
  const [selectedRecording, setSelectedRecording] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [filterDate, setFilterDate] = useState('today');
  const [filterCamera, setFilterCamera] = useState('all');
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [selectionStart, setSelectionStart] = useState(null);
  const [selectionEnd, setSelectionEnd] = useState(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [downloadFormat, setDownloadFormat] = useState('mp4');

  // Refs for video player
  const videoRef = useRef(null);
  const timelineRef = useRef(null);

  // Load available streams and start status polling on component mount
  useEffect(() => {
    loadAvailableStreams();
    startStatusPolling();
    return () => {
      stopStatusPolling();
    };
  }, [loadAvailableStreams, startStatusPolling, stopStatusPolling]);

  // Update filtered recordings when recordings or filters change
  useEffect(() => {
    const filtered = getFilteredRecordings();
    setFilteredRecordings(filtered);
  }, [recordings, filters, getFilteredRecordings]);

  // Enhanced video player functions
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimelineClick = (e) => {
    if (!timelineRef.current || !videoRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    if (isSelecting) {
      if (selectionStart === null) {
        setSelectionStart(newTime);
      } else if (selectionEnd === null) {
        setSelectionEnd(newTime);
        setIsSelecting(false);
      }
    } else {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const handleVideoTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleVideoLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const startSelection = () => {
    setIsSelecting(true);
    setSelectionStart(null);
    setSelectionEnd(null);
  };

  const clearSelection = () => {
    setSelectionStart(null);
    setSelectionEnd(null);
    setIsSelecting(false);
  };

  const jumpToTime = (time) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const downloadSegment = () => {
    if (selectionStart !== null && selectionEnd !== null && selectedRecording) {
      const startTime = Math.min(selectionStart, selectionEnd);
      const endTime = Math.max(selectionStart, selectionEnd);

      // Create download URL with time parameters
      const downloadUrl = `/api/recordings/${selectedRecording.filename}/download?start=${startTime}&end=${endTime}&format=${downloadFormat}`;

      // Create temporary download link
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${selectedRecording.stream_id}_${formatTime(startTime)}-${formatTime(endTime)}.${downloadFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadRecordings(true); // Force refresh
      await loadAvailableStreams();
    } catch (error) {
      console.error('Error refreshing recordings:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleRestart = async () => {
    setIsRestarting(true);
    try {
      await restartRecordings();
      await loadRecordings(true);
    } catch (error) {
      console.error('Error restarting recordings:', error);
    } finally {
      setIsRestarting(false);
    }
  };

  const handlePlayRecording = (recording) => {
    setSelectedRecording(recording);
    if (onSelectRecording) {
      onSelectRecording(recording);
    }
  };

  // Enhanced filter functions
  const handleDateFilterChange = (value) => {
    setFilterDate(value);
    // Update the store filters as well
    setFilters({ ...filters, dateRange: value });
  };

  const handleCameraFilterChange = (value) => {
    setFilterCamera(value);
    // Update the store filters as well
    setFilters({ ...filters, streamId: value === 'all' ? null : value });
  };

  const handleSearchChange = (value) => {
    setSearchQuery(value);
    // Implement search filtering logic here
  };

  // Enhanced UI Components
  const RecordingStatusIndicator = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-black">Recording Status</h3>
        <div className="flex items-center text-sm text-gray-600">
          <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
          System Active
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {availableStreams && availableStreams.length > 0 ? (
          availableStreams.map((stream) => (
            <div key={stream} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Video className="w-4 h-4 mr-2 text-gray-600" />
                <span className="text-sm font-medium text-black">
                  {archiveApi.parseStreamId(stream).collectionName} ({archiveApi.parseStreamId(stream).cameraIp})
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-xs text-gray-600">Active</span>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-3 text-center text-gray-500">
            No active recording streams detected
          </div>
        )}
      </div>
    </div>
  );

  const FilterBar = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <select
            value={filterDate}
            onChange={(e) => handleDateFilterChange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-200"
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="all">All Time</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-600" />
          <select
            value={filterCamera}
            onChange={(e) => handleCameraFilterChange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-200"
          >
            <option value="all">All Cameras</option>
            {availableStreams && availableStreams.map(stream => (
              <option key={stream} value={stream}>
                {archiveApi.parseStreamId(stream).collectionName} ({archiveApi.parseStreamId(stream).cameraIp})
              </option>
            ))}
          </select>
        </div>

        <div className="flex items-center gap-2 flex-1 max-w-xs">
          <Search className="w-4 h-4 text-gray-600" />
          <input
            type="text"
            placeholder="Search recordings..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm w-full focus:outline-none focus:ring-2 focus:ring-gray-200"
          />
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors text-sm"
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
          <button
            onClick={handleRestart}
            disabled={isRestarting}
            className="flex items-center px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors text-sm"
          >
            <RotateCcw className={`w-4 h-4 mr-1 ${isRestarting ? 'animate-spin' : ''}`} />
            {isRestarting ? 'Restarting...' : 'Restart'}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="archive-playback-content">
      <div className="recordings-container">
        {/* Enhanced Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-black">Archive Playback</h1>
        </div>

        <RecordingStatusIndicator />
        <FilterBar />

        {/* Enhanced Recording Card Component */}
        {selectedRecording ? (
          <div className="p-4 lg:p-6">
            <div className="flex items-center mb-4">
              <button
                onClick={() => setSelectedRecording(null)}
                className="flex items-center text-gray-600 hover:text-black mr-4"
              >
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Recordings
              </button>
            </div>

            {/* Enhanced Video Player */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4">
              <div className="bg-gray-900 relative">
                <video
                  ref={videoRef}
                  className="w-full h-64 md:h-96 object-cover"
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onTimeUpdate={handleVideoTimeUpdate}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                >
                  <source src={archiveApi.getRecordingStreamUrl(selectedRecording.stream_id, selectedRecording.filename)} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={handlePlayPause}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-4 rounded-full transition-all"
                  >
                    {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                  </button>
                </div>
              </div>

              {/* Video Controls */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={handlePlayPause}
                      className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      {isPlaying ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
                      {isPlaying ? 'Pause' : 'Play'}
                    </button>
                    <span className="text-sm text-gray-600">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => jumpToTime(0)}
                      className="p-2 hover:bg-gray-100 rounded-lg"
                      title="Go to start"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => jumpToTime(currentTime - 10)}
                      className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                      title="Back 10s"
                    >
                      -10s
                    </button>
                    <button
                      onClick={() => jumpToTime(currentTime + 10)}
                      className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                      title="Forward 10s"
                    >
                      +10s
                    </button>
                  </div>
                </div>

                {/* Timeline */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium text-black">Timeline</label>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={startSelection}
                        className={`px-3 py-1 text-sm rounded-lg ${
                          isSelecting ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 hover:bg-gray-200'
                        }`}
                      >
                        {isSelecting ? 'Selecting...' : 'Select Range'}
                      </button>
                      {(selectionStart !== null || selectionEnd !== null) && (
                        <button
                          onClick={clearSelection}
                          className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                        >
                          Clear
                        </button>
                      )}
                    </div>
                  </div>

                  <div
                    ref={timelineRef}
                    className="relative w-full h-8 bg-gray-200 rounded-lg cursor-pointer"
                    onClick={handleTimelineClick}
                  >
                    {/* Progress bar */}
                    <div
                      className="absolute top-0 left-0 h-full bg-gray-400 rounded-lg"
                      style={{ width: `${(currentTime / duration) * 100}%` }}
                    />

                    {/* Selection overlay */}
                    {selectionStart !== null && selectionEnd !== null && (
                      <div
                        className="absolute top-0 h-full bg-blue-300 bg-opacity-50 rounded-lg"
                        style={{
                          left: `${(Math.min(selectionStart, selectionEnd) / duration) * 100}%`,
                          width: `${(Math.abs(selectionEnd - selectionStart) / duration) * 100}%`
                        }}
                      />
                    )}

                    {/* Current time indicator */}
                    <div
                      className="absolute top-0 w-1 h-full bg-red-500 rounded-full"
                      style={{ left: `${(currentTime / duration) * 100}%` }}
                    />

                    {/* Selection markers */}
                    {selectionStart !== null && (
                      <div
                        className="absolute top-0 w-2 h-full bg-blue-500 rounded-full"
                        style={{ left: `${(selectionStart / duration) * 100}%` }}
                      />
                    )}
                    {selectionEnd !== null && (
                      <div
                        className="absolute top-0 w-2 h-full bg-blue-500 rounded-full"
                        style={{ left: `${(selectionEnd / duration) * 100}%` }}
                      />
                    )}
                  </div>

                  {/* Time markers */}
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>00:00:00</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                </div>

                {/* Selection Info */}
                {selectionStart !== null && selectionEnd !== null && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-blue-800">
                        <div className="font-medium">Selected Range:</div>
                        <div>
                          {formatTime(Math.min(selectionStart, selectionEnd))} - {formatTime(Math.max(selectionStart, selectionEnd))}
                        </div>
                        <div className="text-xs">
                          Duration: {formatTime(Math.abs(selectionEnd - selectionStart))}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <select
                          value={downloadFormat}
                          onChange={(e) => setDownloadFormat(e.target.value)}
                          className="text-sm border border-blue-300 rounded-lg px-2 py-1"
                        >
                          <option value="mp4">MP4</option>
                          <option value="avi">AVI</option>
                          <option value="mov">MOV</option>
                        </select>
                        <button
                          onClick={downloadSegment}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center"
                        >
                          <Download className="w-4 h-4 mr-1" />
                          Download Segment
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Quick Time Jumps */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="text-sm text-gray-600 mr-2">Quick Jump:</span>
                  {[0, 0.25, 0.5, 0.75, 1].map((percentage) => (
                    <button
                      key={percentage}
                      onClick={() => jumpToTime(duration * percentage)}
                      className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      {percentage === 0 ? 'Start' : percentage === 1 ? 'End' : `${percentage * 100}%`}
                    </button>
                  ))}
                </div>
              </div>

              {/* Recording Info */}
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-black">
                    {archiveApi.parseStreamId(selectedRecording.stream_id).collectionName}
                  </h3>
                  <span className="text-sm text-gray-600">
                    {archiveApi.parseStreamId(selectedRecording.stream_id).cameraIp}
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatDate(selectedRecording.timestamp)}
                  </div>
                  <div className="flex items-center">
                    <Video className="w-4 h-4 mr-1" />
                    {archiveApi.formatRecordingDuration(selectedRecording)}
                  </div>
                  <div className="flex items-center">
                    <HardDrive className="w-4 h-4 mr-1" />
                    {archiveApi.formatFileSize(selectedRecording.size_bytes)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="archive-content-area">
            {/* Loading State */}
            {isLoading && (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <h3>Loading Archive</h3>
                <p>Fetching recordings from all cameras...</p>
              </div>
            )}

            {/* Enhanced Error State */}
            {error && (
              <div className="error-container">
                <div className="error-icon">
                  <AlertCircle className="w-12 h-12 text-red-500" />
                </div>
                <h3>Error Loading Recordings</h3>
                <p className="error-message">
                  {error.includes('<!DOCTYPE')
                    ? 'Backend server returned HTML instead of JSON. Please check if the backend is running correctly.'
                    : error
                  }
                </p>
                {error.includes('backend is not available') && (
                  <div className="backend-unavailable-info">
                    <p className="info-text">
                      The archive backend is currently not running. To access recordings:
                    </p>
                    <ul className="info-list">
                      <li>Start the Python backend server</li>
                      <li>Ensure the archive recording service is running</li>
                      <li>Check that recordings exist in the ./recordings/ directory</li>
                    </ul>
                  </div>
                )}
                <button onClick={handleRefresh} className="retry-button">
                  Try Again
                </button>
              </div>
            )}

            {/* Enhanced Recordings Grid */}
            {!isLoading && !error && (
              <div className={`grid gap-4 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {filteredRecordings.length === 0 ? (
                  <div className="col-span-full">
                    <div className="no-recordings">
                      <div className="no-recordings-icon">📁</div>
                      <h3>No archived recordings found</h3>
                      <p>
                        {selectedStreamId
                          ? "No recordings available for the selected camera."
                          : "No recordings are currently available from any camera."
                        }
                      </p>
                      <p className="hint">
                        Recordings will appear here once the cameras start recording or when existing recordings are detected.
                      </p>
                      {selectedStreamId && (
                        <button
                          onClick={() => setFilters({ dateRange: 'all', sortBy: 'newest' })}
                          className="clear-filter-button"
                        >
                          Show All Recordings
                        </button>
                      )}
                    </div>
                  </div>
                ) : (
                  filteredRecordings.map(recording => (
                    <div
                      key={recording.filename}
                      className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => handlePlayRecording(recording)}
                    >
                      <div className="relative">
                        <div className="w-full h-32 bg-gray-100 flex items-center justify-center">
                          <Video className="w-8 h-8 text-gray-400" />
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          {archiveApi.formatRecordingDuration(recording)}
                        </div>
                      </div>
                      <div className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-black text-sm">
                            {archiveApi.parseStreamId(recording.stream_id).collectionName}
                          </h4>
                          <span className="text-xs text-gray-600">
                            {archiveApi.formatFileSize(recording.size_bytes)}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600 mb-1">
                          {formatDate(recording.timestamp)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {archiveApi.parseStreamId(recording.stream_id).cameraIp}
                        </div>
                        <div className="flex items-center justify-between mt-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePlayRecording(recording);
                            }}
                            className="flex items-center px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors text-xs"
                          >
                            <Play className="w-3 h-3 mr-1" />
                            Play
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              // Handle delete functionality
                              console.log('Delete recording:', recording.filename);
                            }}
                            className="p-1 hover:bg-red-100 text-red-600 rounded-lg transition-colors"
                            title="Delete recording"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(ArchivePlaybackContent);

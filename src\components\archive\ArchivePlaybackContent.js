// Enhanced Archive Playback with modern UI
import React from 'react';
import EnhancedArchivePlayback from './EnhancedArchivePlayback';
import './ArchivePlaybackContent.css';

const ArchivePlaybackContent = ({ onSelectRecording, selectedRecordingId }) => {
  return (
    <EnhancedArchivePlayback
      onSelectRecording={onSelectRecording}
      selectedRecordingId={selectedRecordingId}
    />
  );
};

export default React.memo(ArchivePlaybackContent);

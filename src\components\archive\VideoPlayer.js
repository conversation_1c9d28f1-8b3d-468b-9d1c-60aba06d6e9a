import React, { useState, useRef, useEffect } from 'react';
import './VideoPlayer.css';

/**
 * Enhanced video player component for archive playback with error handling and recovery
 */
const VideoPlayer = ({ src, recording, onError }) => {
  const videoRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [videoReady, setVideoReady] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const MAX_RETRY_ATTEMPTS = 3;

  // Reset state when src changes and test URL accessibility
  useEffect(() => {
    setIsLoading(true);
    setError(null);
    setRetryCount(0);
    setVideoReady(false);
    setCurrentTime(0);
    setDuration(0);

    // Test if the video URL is accessible
    if (src) {
      console.log('=== VIDEO URL DEBUG ===');
      console.log('Video src URL:', src);
      console.log('Recording object:', recording);
      console.log('Recording streamId:', recording?.streamId);
      console.log('Recording filename:', recording?.filename);

      // Test URL accessibility with detailed error handling
      fetch(src, {
        method: 'HEAD',
        headers: {
          'Range': 'bytes=0-1023'
        }
      })
      .then(response => {
        console.log('Video URL test response:', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          url: response.url,
          ok: response.ok
        });
        if (!response.ok) {
          console.error('Video URL not accessible:', response.status, response.statusText);

          // Try a simple GET request to see if it's a HEAD request issue
          return fetch(src, {
            method: 'GET',
            headers: {
              'Range': 'bytes=0-1023'
            }
          });
        }
        return response;
      })
      .then(response => {
        if (response && response.status !== 200 && response.status !== 206) {
          console.log('GET request also failed:', response.status, response.statusText);
        } else if (response) {
          console.log('GET request successful:', response.status);
        }
      })
      .catch(error => {
        console.error('Video URL test failed:', error);
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      });
    }
  }, [src]);

  // Handle video loading
  const handleLoadStart = () => {
    console.log('Video loading started:', src);
    console.log('Video element:', videoRef.current);
    setIsLoading(true);
    setError(null);
  };

  const handleLoadedMetadata = () => {
    console.log('Video metadata loaded:', src);
    if (videoRef.current) {
      console.log('Video duration:', videoRef.current.duration);
      console.log('Video dimensions:', videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
      setDuration(videoRef.current.duration);
    }
  };

  const handleLoadedData = () => {
    console.log('Video data loaded:', src);
    console.log('Video ready state:', videoRef.current?.readyState);
    setIsLoading(false);
    setVideoReady(true);
  };

  const handleCanPlay = () => {
    console.log('Video can play:', src);
    console.log('Video buffered ranges:', videoRef.current?.buffered?.length || 0);
    setIsLoading(false);
    setVideoReady(true);
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleError = (event) => {
    const video = videoRef.current;
    let errorMessage = 'Unknown video error';
    let errorCode = 'UNKNOWN';
    let detailedInfo = {};

    // Collect detailed error information
    if (video) {
      detailedInfo = {
        networkState: video.networkState,
        readyState: video.readyState,
        currentSrc: video.currentSrc,
        duration: video.duration,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        error: video.error ? {
          code: video.error.code,
          message: video.error.message
        } : null
      };

      if (video.error) {
        switch (video.error.code) {
          case video.error.MEDIA_ERR_ABORTED:
            errorMessage = 'Video playback was aborted by user or browser';
            errorCode = 'ABORTED';
            break;
          case video.error.MEDIA_ERR_NETWORK:
            errorMessage = 'Network error occurred while loading video. Check your connection and server status.';
            errorCode = 'NETWORK';
            break;
          case video.error.MEDIA_ERR_DECODE:
            errorMessage = 'Video format is not supported or file is corrupted. The video codec may not be compatible with your browser.';
            errorCode = 'DECODE';
            break;
          case video.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'Video format is not supported by your browser. Try using a different browser or check the video encoding.';
            errorCode = 'FORMAT';
            break;
          default:
            errorMessage = `Video error (code: ${video.error.code}): ${video.error.message || 'Unknown error'}`;
            errorCode = `CODE_${video.error.code}`;
        }
      } else {
        // No video.error object - this often indicates streaming or format issues
        if (video.networkState === video.NETWORK_NO_SOURCE) {
          errorMessage = 'No video source available. The streaming URL may be invalid.';
          errorCode = 'NO_SOURCE';
        } else if (video.networkState === video.NETWORK_LOADING) {
          errorMessage = 'Video is still loading. This may indicate a slow connection or server issue.';
          errorCode = 'LOADING_TIMEOUT';
        } else if (video.readyState === video.HAVE_NOTHING) {
          errorMessage = 'No video data available. The file may be corrupted or the server may be unreachable.';
          errorCode = 'NO_DATA';
        } else {
          errorMessage = 'Unknown video error. Check browser console for more details.';
          errorCode = 'UNKNOWN';
        }
      }
    }

    console.error('Video playback error:', {
      message: errorMessage,
      code: errorCode,
      src: src,
      retryCount: retryCount,
      detailedInfo: detailedInfo,
      event: event
    });

    setIsLoading(false);
    setError({ message: errorMessage, code: errorCode, detailedInfo });

    if (onError) {
      onError({ message: errorMessage, code: errorCode, src: src, detailedInfo });
    }
  };

  const validateVideoFile = async () => {
    // Handle both streamId and stream_id property names
    const streamId = recording?.streamId || recording?.stream_id;
    const filename = recording?.filename;

    if (!streamId || !filename) {
      console.error('Missing streamId or filename for validation:', { streamId, filename, recording });
      return null;
    }

    try {
      console.log('Validating video file:', { streamId, filename });
      const response = await fetch(`http://localhost:8000/api/archive/validate/${streamId}/${filename}`);
      if (response.ok) {
        const validation = await response.json();
        console.log('Video validation result:', validation);

        // Enhanced validation reporting
        if (!validation.is_valid) {
          console.warn('Video file validation failed:', validation.issues);
        }

        return validation;
      } else {
        console.error('Validation request failed:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Validation error details:', errorText);
      }
    } catch (error) {
      console.error('Error validating video file:', error);
    }
    return null;
  };

  const refreshStreamsList = async () => {
    try {
      console.log('Refreshing streams list with force refresh...');
      const response = await fetch('http://localhost:8000/api/archive/streams?force_refresh=true');
      if (response.ok) {
        const data = await response.json();
        console.log('Refreshed streams data:', data);
        return data;
      } else {
        console.error('Failed to refresh streams:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error refreshing streams:', error);
    }
    return null;
  };

  const handleRetry = async () => {
    if (retryCount < MAX_RETRY_ATTEMPTS) {
      console.log(`Retrying video playback (attempt ${retryCount + 1}/${MAX_RETRY_ATTEMPTS}):`, src);
      setRetryCount(prev => prev + 1);
      setError(null);
      setIsLoading(true);
      setVideoReady(false);

      // Validate the video file before retrying
      const validation = await validateVideoFile();
      if (validation && !validation.is_valid) {
        console.error('Video file validation failed:', validation.issues);
        setError({
          message: `Video file is invalid: ${validation.issues.join(', ')}`,
          code: 'INVALID_FILE',
          validation
        });
        setIsLoading(false);
        return;
      }

      // Force reload the video
      if (videoRef.current) {
        videoRef.current.load();
      }
    }
  };

  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <div className="video-player-error">
        <div className="error-content">
          <div className="error-icon">⚠️</div>
          <h3>Video Playback Error</h3>
          <p className="error-message">{error.message}</p>
          <div className="error-details">
            <p><strong>File:</strong> {recording?.filename || 'Unknown'}</p>
            <p><strong>Size:</strong> {recording?.size ? `${recording.size} MB` : 'Unknown'}</p>
            <p><strong>Error Code:</strong> {error.code}</p>
          </div>
          <div className="error-actions">
            {retryCount < MAX_RETRY_ATTEMPTS && (
              <button
                className="retry-button"
                onClick={handleRetry}
                disabled={isLoading}
              >
                {isLoading ? 'Retrying...' : `Retry (${retryCount}/${MAX_RETRY_ATTEMPTS})`}
              </button>
            )}
            <button
              className="validate-button"
              onClick={async () => {
                const validation = await validateVideoFile();
                if (validation) {
                  alert(`File validation result:\n\nValid: ${validation.is_valid}\nSize: ${validation.file_size_mb} MB\nMP4 Signature: ${validation.has_mp4_signature}\nIssues: ${validation.issues?.join(', ') || 'None'}`);
                } else {
                  alert('Unable to validate file');
                }
              }}
            >
              Validate File
            </button>
            <button
              className="download-button"
              onClick={() => window.open(src, '_blank')}
            >
              Download File
            </button>
            <button
              className="refresh-button"
              onClick={async () => {
                const refreshed = await refreshStreamsList();
                if (refreshed) {
                  alert('Streams list refreshed. Please try selecting the recording again.');
                } else {
                  alert('Failed to refresh streams list');
                }
              }}
            >
              Refresh Files
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="video-player-container">
      {isLoading && (
        <div className="video-loading-overlay">
          <div className="loading-spinner"></div>
          <p>Loading video...</p>
          {retryCount > 0 && (
            <p className="retry-info">Retry attempt {retryCount}/{MAX_RETRY_ATTEMPTS}</p>
          )}
        </div>
      )}
      
      <video
        ref={videoRef}
        className={`archive-video-player ${videoReady ? 'ready' : ''}`}
        controls
        preload="metadata"
        crossOrigin="anonymous"
        playsInline
        onLoadStart={handleLoadStart}
        onLoadedMetadata={handleLoadedMetadata}
        onLoadedData={handleLoadedData}
        onCanPlay={handleCanPlay}
        onTimeUpdate={handleTimeUpdate}
        onError={handleError}
        onStalled={() => console.log('Video stalled')}
        onSuspend={() => console.log('Video suspended')}
        onWaiting={() => console.log('Video waiting')}
        onProgress={() => console.log('Video progress')}
        style={{
          opacity: videoReady ? 1 : 0,
          transition: 'opacity 0.3s ease'
        }}
        aria-label="Archive recording video player"
      >
        <source src={src} type="video/mp4" />
        <p>Your browser does not support the video tag.</p>
      </video>

      {videoReady && (
        <div className="video-info-overlay">
          <div className="video-progress-info">
            <span className="current-time">{formatTime(currentTime)}</span>
            <span className="duration">{formatTime(duration)}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;

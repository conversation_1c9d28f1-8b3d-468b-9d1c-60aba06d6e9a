import React from 'react';
import { render, fireEvent, screen, act } from '@testing-library/react';
import DirectWebRTCPlayer from '../DirectWebRTCPlayer';

// Mock socket.io-client
jest.mock('socket.io-client', () => {
  const mockSocket = {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  };
  return {
    io: jest.fn(() => mockSocket),
  };
});

describe('DirectWebRTCPlayer', () => {
  const mockRtspUrl = 'rtsp://example.com/stream';
  const mockOnError = jest.fn();
  const mockOnPlay = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    expect(screen.getByRole('video')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    expect(screen.getByText(/connecting to stream/i)).toBeInTheDocument();
  });

  it('handles video load success', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    
    const video = screen.getByRole('video');
    fireEvent.loadedData(video);
    
    expect(mockOnPlay).toHaveBeenCalled();
  });

  it('handles video load error', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    
    const video = screen.getByRole('video');
    fireEvent.error(video);
    
    expect(mockOnError).toHaveBeenCalledWith('Failed to load video stream');
  });

  it('supports zoom controls', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    
    const video = screen.getByRole('video');
    fireEvent.loadedData(video); // Simulate video loaded to show zoom controls
    
    const container = video.parentElement;
    
    // Test mouse wheel zoom
    fireEvent.wheel(container, { deltaY: -100 }); // Zoom in
    expect(container.className).toContain('zoomed');
    
    // Test double click zoom
    fireEvent.doubleClick(container);
    expect(container.className).toContain('zoomed');
    
    fireEvent.doubleClick(container);
    expect(container.className).not.toContain('zoomed');
  });

  it('supports panning when zoomed', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    
    const video = screen.getByRole('video');
    fireEvent.loadedData(video);
    
    const container = video.parentElement;
    
    // Zoom in first
    fireEvent.wheel(container, { deltaY: -100 });
    
    // Test panning
    fireEvent.mouseDown(container, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(container, { clientX: 150, clientY: 150 });
    
    const transform = video.style.transform;
    expect(transform).toContain('translate');
    
    fireEvent.mouseUp(container);
  });

  it('shows zoom buttons when video is playing', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    
    const video = screen.getByRole('video');
    fireEvent.loadedData(video);
    
    expect(screen.getByText('+')).toBeInTheDocument();
    expect(screen.getByText('−')).toBeInTheDocument();
  });

  it('resets zoom and position when reset button is clicked', () => {
    render(
      <DirectWebRTCPlayer
        rtspUrl={mockRtspUrl}
        onError={mockOnError}
        onPlay={mockOnPlay}
      />
    );
    
    const video = screen.getByRole('video');
    fireEvent.loadedData(video);
    
    const container = video.parentElement;
    
    // Zoom in
    fireEvent.wheel(container, { deltaY: -100 });
    
    // Pan
    fireEvent.mouseDown(container, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(container, { clientX: 150, clientY: 150 });
    fireEvent.mouseUp(container);
    
    // Click reset button
    const resetButton = screen.getByText('↺');
    fireEvent.click(resetButton);
    
    expect(container.className).not.toContain('zoomed');
    expect(video.style.transform).toContain('scale(1)');
    expect(video.style.transform).toContain('translate(0px, 0px)');
  });
});
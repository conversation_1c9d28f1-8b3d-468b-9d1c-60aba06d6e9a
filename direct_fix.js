// Direct Fix Script for VMS Application
// This script directly modifies the application state to remove test_collection entries

(function() {
  console.log("Starting direct fix for test_collection entries...");
  
  // Function to find React component instances in the DOM
  function findReactComponents() {
    const components = [];
    const rootElems = document.querySelectorAll('[data-reactroot]');
    
    if (rootElems.length === 0) {
      console.log("No React root elements found. Trying alternative method...");
      // Try to find React instances using internal properties
      const allNodes = document.querySelectorAll('*');
      for (let i = 0; i < allNodes.length; i++) {
        const node = allNodes[i];
        // Look for React fiber nodes
        for (const key in node) {
          if (key.startsWith('__reactFiber$') || 
              key.startsWith('__reactInternalInstance$') ||
              key.startsWith('__reactContainer$')) {
            components.push(node);
            break;
          }
        }
      }
    } else {
      components.push(...rootElems);
    }
    
    console.log(`Found ${components.length} potential React components`);
    return components;
  }
  
  // Function to find and update React component state
  function updateReactComponentState() {
    const components = findReactComponents();
    let updated = false;
    
    components.forEach(component => {
      // Try to access React fiber
      let fiber = null;
      for (const key in component) {
        if (key.startsWith('__reactFiber$') || 
            key.startsWith('__reactInternalInstance$') ||
            key.startsWith('__reactContainer$')) {
          fiber = component[key];
          break;
        }
      }
      
      if (!fiber) return;
      
      // Walk up the fiber tree to find components with state
      let current = fiber;
      while (current) {
        // Check if this fiber has memoizedState
        if (current.memoizedState) {
          // Check if state contains collections
          if (current.memoizedState.collections || 
              (current.memoizedState.element && 
               current.memoizedState.element.props && 
               current.memoizedState.element.props.collections)) {
            
            console.log("Found component with collections in state!");
            
            // Try to update collections
            try {
              let collections = current.memoizedState.collections || 
                               (current.memoizedState.element && 
                                current.memoizedState.element.props && 
                                current.memoizedState.element.props.collections);
              
              if (Array.isArray(collections)) {
                const originalLength = collections.length;
                collections = collections.filter(collection => 
                  !(collection.name && collection.name.toLowerCase().includes('test_collection'))
                );
                
                if (collections.length < originalLength) {
                  console.log(`Removed ${originalLength - collections.length} test_collection entries from component state`);
                  
                  // Update the state
                  if (current.memoizedState.collections) {
                    current.memoizedState.collections = collections;
                  } else if (current.memoizedState.element && 
                            current.memoizedState.element.props && 
                            current.memoizedState.element.props.collections) {
                    current.memoizedState.element.props.collections = collections;
                  }
                  
                  updated = true;
                }
              }
            } catch (e) {
              console.error("Error updating collections in component state:", e);
            }
          }
        }
        
        // Move to the next fiber
        current = current.return;
      }
    });
    
    return updated;
  }
  
  // Try to directly update React component state
  const updated = updateReactComponentState();
  
  if (updated) {
    console.log("Successfully updated React component state!");
    console.log("Forcing a re-render...");
    
    // Try to force a re-render by simulating a window resize
    window.dispatchEvent(new Event('resize'));
    
    // Also try to force a navigation event if the app uses a router
    if (window.history && window.history.pushState) {
      const currentPath = window.location.pathname;
      window.history.pushState({}, '', currentPath + '?refresh=' + Date.now());
      window.history.pushState({}, '', currentPath);
    }
    
    alert("Successfully removed test_collection entries from the UI. The page will now refresh.");
    window.location.reload();
  } else {
    console.log("Could not directly update React component state.");
    console.log("Falling back to localStorage cleanup...");
    
    // Clear localStorage as a fallback
    let removed = false;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      
      if (value && value.toLowerCase().includes('test_collection')) {
        console.log(`Removing localStorage key: ${key}`);
        localStorage.removeItem(key);
        removed = true;
        i--; // Adjust index since we removed an item
      }
    }
    
    if (removed) {
      alert("Removed test_collection references from localStorage. The page will now refresh.");
      window.location.reload();
    } else {
      alert("Could not find any test_collection references. Please try the force_reset.html tool instead.");
    }
  }
})();

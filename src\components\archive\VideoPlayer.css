/* Video Player Component Styles */

.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.archive-video-player {
  width: 100%;
  height: 100%;
  max-height: 70vh;
  object-fit: contain;
  background: #000;
  border: none;
  outline: none;
}

.archive-video-player.ready {
  opacity: 1;
}

/* Loading Overlay */
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #00E5FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.video-loading-overlay p {
  margin: 4px 0;
  font-size: 16px;
  color: #cccccc;
}

.retry-info {
  font-size: 14px !important;
  color: #00E5FF !important;
  font-weight: 500;
}

/* Error Display */
.video-player-error {
  width: 100%;
  height: 100%;
  min-height: 300px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px;
  box-sizing: border-box;
}

.error-content {
  text-align: center;
  max-width: 500px;
  color: #ffffff;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.error-content h3 {
  color: #ff6b6b;
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.error-message {
  color: #cccccc;
  font-size: 16px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.error-details {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
  text-align: left;
}

.error-details p {
  margin: 8px 0;
  font-size: 14px;
  color: #cccccc;
}

.error-details strong {
  color: #ffffff;
  font-weight: 600;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.retry-button,
.download-button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.retry-button {
  background: #00E5FF;
  color: #000000;
}

.retry-button:hover:not(:disabled) {
  background: #00B8CC;
  transform: translateY(-1px);
}

.retry-button:disabled {
  background: #666666;
  color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

.download-button {
  background: transparent;
  color: #00E5FF;
  border: 2px solid #00E5FF;
}

.download-button:hover {
  background: #00E5FF;
  color: #000000;
  transform: translateY(-1px);
}

/* Video Info Overlay */
.video-info-overlay {
  position: absolute;
  bottom: 60px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 6px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-player-container:hover .video-info-overlay {
  opacity: 1;
}

.video-progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-time {
  color: #00E5FF;
  font-weight: 600;
}

.duration {
  color: #cccccc;
}

.current-time::after {
  content: ' / ';
  color: #666666;
  font-weight: normal;
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-player-error {
    padding: 24px 16px;
    min-height: 250px;
  }
  
  .error-content h3 {
    font-size: 20px;
  }
  
  .error-message {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .retry-button,
  .download-button {
    width: 100%;
    max-width: 200px;
  }
  
  .video-info-overlay {
    bottom: 50px;
    right: 12px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .archive-video-player {
    max-height: 50vh;
  }
  
  .error-content {
    padding: 0 8px;
  }
  
  .error-details {
    padding: 12px;
    font-size: 12px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
  
  .retry-button:hover:not(:disabled),
  .download-button:hover {
    transform: none;
  }
  
  .archive-video-player {
    transition: none;
  }
  
  .video-info-overlay {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .video-player-error {
    background: #000000;
    border: 2px solid #ffffff;
  }
  
  .error-content h3 {
    color: #ffffff;
  }
  
  .retry-button {
    background: #ffffff;
    color: #000000;
    border: 2px solid #ffffff;
  }
  
  .download-button {
    background: #000000;
    color: #ffffff;
    border: 2px solid #ffffff;
  }
}

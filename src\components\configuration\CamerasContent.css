.cameras-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* Content Toolbar - New professional toolbar */
.content-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  margin-bottom: 16px;
  background-color: rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #FFFFFF;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Base button styles */
.add-collection-button,
.add-camera-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px; /* Spacing between icon and text */
  height: 40px; /* Fixed height */
  min-width: 140px; /* Minimum width */
  padding: 0 16px; /* Horizontal padding */
  border-radius: 4px; /* Smaller border radius */
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Primary Button - Manage Collection */
.add-collection-button {
  background-color: #FFFFFF; /* White color to match theme */
  color: #000000;
  border: 1px solid #000000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-collection-button:hover {
  background-color: #F8F9FA; /* Light gray */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.add-collection-button:active {
  background-color: #E9ECEF; /* Darker gray */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Secondary Button - Add New Camera */
.add-camera-button {
  background-color: rgba(0, 0, 0, 0.05);
  color: #000000; /* Match primary color */
  border: 1px solid #000000; /* Match primary color */
}

.add-camera-button:hover {
  background-color: rgba(0, 0, 0, 0.1); /* 10% opacity of primary color */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-camera-button:active {
  background-color: rgba(0, 0, 0, 0.2); /* 20% opacity of primary color */
}

/* Button icon styles */
.button-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.add-collection-button .button-icon {
  filter: brightness(0) invert(1); /* Make icon white */
}

/* Focus states for accessibility */
.add-collection-button:focus,
.add-camera-button:focus {
  outline: 2px solid #000000;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2);
}

/* Main content area */
.cameras-content-main {
  flex: 1;
  padding: 0 24px 24px;
  overflow-y: auto;
  min-height: 0; /* Allow flex child to shrink */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
  }

  .toolbar-actions {
    width: 100%;
    flex-direction: column;
  }

  .add-collection-button,
  .add-camera-button {
    width: 100%;
  }
}

.cameras-list {
  flex: 1;
  overflow-y: auto;
}

.collection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.collection-name {
  font-weight: 500;
}

.collection-count {
  color: #6c757d;
  font-size: 14px;
}
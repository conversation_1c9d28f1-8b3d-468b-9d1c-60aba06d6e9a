/* New Login Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

:root {
  --theme-signup: #000000; /* Using black */
  --theme-signup-darken: #343A40;
  --theme-signup-background: #FFFFFF; /* Using white */
  --theme-login: #000000; /* Using black */
  --theme-login-darken: #343A40; /* Using dark gray */
  --theme-login-background: #FFFFFF; /* Using white */
  --theme-dark: #000000; /* Using black */
  --theme-light: #FFFFFF;
  --success: #000000; /* Using black */
  --error: #000000; /* Using black */
}

body {
  margin: 0;
  height: 100%;
  overflow: hidden;
  width: 100% !important;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
}

.backRight {
  position: absolute;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, #00E5FF, #7A32FF); /* Using ravia-accent-gradient */
}

.backLeft {
  position: absolute;
  left: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, #1E0B38, #3A1670); /* Using ravia-purple gradient */
}

#back {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -999;
}

.canvas-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

#slideBox {
  width: 50%;
  max-height: 100%;
  height: 100%;
  overflow: hidden;
  margin-left: 50%;
  position: absolute;
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.topLayer {
  width: 200%;
  height: 100%;
  position: relative;
  left: 0;
  left: -100%;
}

.login-container {
  margin: 0;
  height: 100vh;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
  position: relative;
}

.left {
  width: 50%;
  height: 100%;
  overflow: auto;
  background: var(--theme-signup-background);
  left: 0;
  position: absolute;
}

.left label {
  color: var(--theme-light);
  font-size: 0.8em;
  text-transform: uppercase;
}

.left input {
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 1em;
  padding: 8px 1px;
  margin-top: 0.1em;
  border-bottom: 1px solid var(--theme-light);
  color: var(--theme-light);
}

.left input:focus,
.left input:active {
  border-color: var(--theme-signup);
  color: var(--theme-signup);
}

.left input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--theme-signup-background) inset;
  -webkit-text-fill-color: var(--theme-light);
}

.left a {
  color: var(--theme-signup);
}

.right {
  width: 50%;
  height: 100%;
  overflow: auto;
  background: var(--theme-login-background);
  right: 0;
  position: absolute;
}

.right label {
  color: var(--theme-dark);
  font-size: 0.8em;
  text-transform: uppercase;
}

.right input {
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 1em;
  padding: 8px 1px;
  margin-top: 0.1em;
  border-bottom: 1px solid var(--theme-dark);
}

.right input:focus,
.right input:active {
  border-color: var(--theme-login);
}

.right input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--theme-login-background) inset;
  -webkit-text-fill-color: var(--theme-dark);
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100%;
  width: 80%;
  margin: 0 auto;
  position: relative;
}

.content h2 {
  font-weight: 300;
  font-size: 2.6em;
  margin: 0.2em 0 0.1em;
}

.left .content h2 {
  color: var(--theme-signup);
}

.right .content h2 {
  color: var(--theme-login);
}

.form-element {
  margin: 1.6em 0;
}

.form-element.form-submit {
  margin: 1.6em 0 0;
}

.form-stack {
  display: flex;
  flex-direction: column;
}

.checkbox {
  -webkit-appearance: none;
  outline: none;
  background-color: var(--theme-light);
  border: 1px solid var(--theme-light);
  box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
  padding: 12px;
  border-radius: 4px;
  display: inline-block;
  position: relative;
}

.checkbox:focus,
.checkbox:checked:focus,
.checkbox:active,
.checkbox:checked:active {
  border-color: var(--theme-signup);
  box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.checkbox:checked {
  outline: none;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1);
}

.checkbox:checked:after {
  outline: none;
  content: '\2713';
  color: var(--theme-signup);
  font-size: 1.4em;
  font-weight: 900;
  position: absolute;
  top: -4px;
  left: 4px;
}

.form-checkbox {
  display: flex;
  align-items: center;
}

.form-checkbox label {
  margin: 0 6px 0;
  font-size: 0.72em;
}

button {
  padding: 0.8em 1.2em;
  margin: 0 10px 0 0;
  width: auto;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 1em;
  color: #fff;
  line-height: 1em;
  letter-spacing: 0.6px;
  border-radius: 3px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1), 0 3px 6px rgba(0,0,0,0.1);
  border: 0;
  outline: 0;
  transition: all 0.25s;
}

button.signup {
  background: var(--theme-signup);
}

button.login {
  background: var(--theme-login);
}

button.off {
  background: none;
  box-shadow: none;
  margin: 0;
}

button.off.signup {
  color: var(--theme-signup);
}

button.off.login {
  color: var(--theme-login);
}

button:focus,
button:active,
button:hover {
  box-shadow: 0 4px 7px rgba(0,0,0,0.1), 0 3px 6px rgba(0,0,0,0.1);
}

button.signup:focus,
button.signup:active,
button.signup:hover {
  background: var(--theme-signup-darken);
}

button.login:focus,
button.login:active,
button.login:hover {
  background: var(--theme-login-darken);
}

button.off:focus,
button.off:active,
button.off:hover {
  box-shadow: none;
}

button.off.signup:focus,
button.off.signup:active,
button.off.signup:hover {
  color: var(--theme-signup);
  background: var(--theme-dark);
}

button.off.login:focus,
button.off.login:active,
button.off.login:hover {
  color: var(--theme-login-darken);
  background: var(--theme-light);
}

@media only screen and (max-width: 768px) {
  #slideBox {
    width: 80%;
    margin-left: 20%;
  }

  .signup-info,
  .login-info {
    display: none;
  }
}

.login-error {
  background-color: rgba(213, 79, 79, 0.1);
  border-left: 4px solid var(--error);
  padding: 12px 15px;
  color: var(--error);
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 5px;
}

.login-logo {
  width: 100px;
  height: auto;
  margin-bottom: 15px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

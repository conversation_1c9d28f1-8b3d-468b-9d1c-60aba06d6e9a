import React, { useState, useEffect, useRef } from 'react';
import './ConfigurationSidebar.css';
import '../sidebar/ModernSidebar.css'; // Import the new modern sidebar styles
import {
  Person as UserIcon,
  Storage as StorageIcon,
  Videocam as CctvIcon,
  Notifications as AlertIcon,
  Map as MapIcon,
  Link as ExternalIcon,
  CloudSync as DrSitesIcon,
  Settings as TrackSettingsIcon,
  Event as EventIcon,
  Mail as MailIcon,
  Send as SenderIcon,
  VideoSettings as VideoDecoderIcon,
  Analytics as AnalyticIcon,
  Computer as MediaServerIcon,
  Folder as Storage2Icon,
  PushPin as PinIcon,
  Security as SecureAccessIcon
} from '@mui/icons-material';

import addIcon from '../../icon/add-icon.png';
import AddCollectionModal from './AddCollectionModal';

const ConfigurationSidebar = ({ onViewChange, currentUser }) => {
  const [activeItem, setActiveItem] = useState(null);
  const [expandedItems, setExpandedItems] = useState({});
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Ref for the sidebar element
  const sidebarRef = useRef(null);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Reset expanded state when switching between mobile and desktop
      if (!mobile) {
        setSidebarExpanded(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle sidebar expansion for mobile view
  const toggleSidebar = () => {
    setSidebarExpanded(prev => !prev);
  };

  const toggleDropdown = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const handleMenuItemClick = (itemId) => {
    setActiveItem(itemId);
    if (itemId === 'cameras') {
      onViewChange('cameras');
    } else {
      onViewChange(null); // Reset view when selecting other menu items
    }
    if (menuItems.find(item => item.id === itemId)?.hasDropdown) {
      toggleDropdown(itemId);
    }
  };

  const handleDropdownItemClick = (parentId, dropdownItem) => {
    // Convert dropdown item label to a view identifier
    const viewId = dropdownItem.label.toLowerCase().replace(/\s+/g, '-');
    console.log('Dropdown item clicked:', viewId);
    onViewChange(viewId);
    setActiveItem(`${parentId}-${viewId}`);
  };

  // Check if user has permission to manage users
  console.log('Current user in ConfigurationSidebar:', currentUser);

  // Always allow Admin users to manage users (Supervisors)
  const canManageUsers = currentUser && (
    currentUser.role === 'SuperAdmin' ||
    currentUser.role === 'Admin'
  );

  const menuItems = [
    {
      id: 'users',
      label: 'Users',
      icon: <UserIcon />,
      hasDropdown: true,
      dropdownItems: [
        { label: 'User Access Levels', icon: <SecureAccessIcon /> }
      ]
    },
    {
      id: 'storage-server',
      label: 'Storage & Server',
      icon: <StorageIcon />,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Storage', icon: <Storage2Icon /> },
        { label: 'Media Server', icon: <MediaServerIcon /> },
        { label: 'Analytics Server', icon: <AnalyticIcon /> },
        { label: 'Video Decoder Connector', icon: <VideoDecoderIcon /> }
      ]
    },
    {
      id: 'cameras',
      label: 'Cameras',
      icon: <CctvIcon />,
      hasDropdown: false
    },
    {
      id: 'alerts',
      label: 'Alerts',
      icon: <AlertIcon />,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Sender Configuration', icon: <SenderIcon /> },
        { label: 'Receiver Configuration', icon: <MailIcon /> },
        { label: 'Event Distribution', icon: <EventIcon /> }
      ]
    },
    {
      id: 'map',
      label: 'Map',
      icon: <MapIcon />,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Basic Map', icon: <MapIcon /> },
        { label: 'Google Map', icon: <PinIcon /> }
      ]
    },
    {
      id: 'dr-sites',
      label: 'DR Sites',
      icon: <DrSitesIcon />,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Replication Policy', icon: <DrSitesIcon /> }
      ]
    },
    // {
    //   id: 'track-settings',
    //   label: 'Track Settings',
    //   icon: <TrackSettingsIcon />,
    //   hasDropdown: false
    // }
  ];

  return (
    <>
      {isMobile && (
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={sidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarExpanded ? "✕" : "☰"}
        </button>
      )}
      <div
        ref={sidebarRef}
        className="universal-sidebar-content"
        role="navigation"
        aria-label="Configuration navigation"
      >
        {menuItems.map((item) => {
          // Skip the Users section if user doesn't have permission to manage users
          if (item.id === 'users' && !canManageUsers) {
            return null;
          }

          return (
            <div key={item.id} className="sidebar-section">
              <button
                className={`sidebar-btn ${activeItem === item.id ? 'active' : ''}`}
                onClick={() => handleMenuItemClick(item.id)}
                aria-expanded={item.hasDropdown ? !!expandedItems[item.id] : undefined}
                aria-controls={item.hasDropdown ? `${item.id}-dropdown` : undefined}
              >
                <span className="sidebar-icon">{item.icon}</span>
                <span>{item.label}</span>
                {item.hasDropdown && (
                  <span className={`chevron-icon ${expandedItems[item.id] ? 'expanded' : ''}`}>
                    {expandedItems[item.id] ? "↑" : "↓"}
                  </span>
                )}
                {isMobile && !sidebarExpanded && (
                  <span className="sidebar-tooltip">{item.label}</span>
                )}
              </button>
            {item.hasDropdown && expandedItems[item.id] && (
              <div id={`${item.id}-dropdown`} className="sidebar-dropdown">
                {item.dropdownItems.map((dropdownItem, index) => (
                  <div
                    key={index}
                    className="dropdown-item"
                    onClick={() => handleDropdownItemClick(item.id, dropdownItem)}
                    role="button"
                    tabIndex={0}
                  >
                    <span className="sidebar-icon">{dropdownItem.icon}</span>
                    <span>{dropdownItem.label}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
      </div>
    </>
  );
};

export default ConfigurationSidebar;
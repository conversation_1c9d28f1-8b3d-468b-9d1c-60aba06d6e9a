.map-container {
  background: #1e1e1e;
  border-radius: 8px;
  padding: 15px;
  margin: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.map-header {
  margin-bottom: 10px;
}

.map-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 16px;
}

.map-content {
  position: relative;
  width: 100%;
  height: 300px;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: #2a2a2a;
  border-radius: 4px;
  position: relative;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.camera-marker {
  position: absolute;
  width: 24px;
  height: 24px;
  background: #E37814;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.camera-marker:hover {
  transform: translate(-50%, -50%) scale(1.2);
  background: #ff8c1a;
}

.marker-label {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.basic-map {
  border: 1px solid #333;
}

.global-map {
  border: 1px solid #444;
}
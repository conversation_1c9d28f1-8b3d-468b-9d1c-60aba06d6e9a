#!/usr/bin/env python3
"""
Simple test for video encoding value mapping
"""

def test_video_encoding_mapping():
    """Test video encoding value mapping"""
    print("Testing video encoding value mapping...")
    
    # Test cases for different input formats
    test_cases = [
        ('H.264', 'H264'),
        ('H.265', 'H265'),
        ('h264', 'H264'),
        ('h265', 'H264'),
        ('H264', 'H264'),
        ('H265', 'H265'),
        ('0', 'H264'),  # Angular numeric value for H.264
        ('1', 'H265'),  # Angular numeric value for H.265
        ('HEVC', 'H265'),
    ]
    
    print("\nTesting encoding value normalization:")
    for input_value, expected_output in test_cases:
        # Simulate the normalization logic from the ONVIF service
        if input_value.lower() in ['h264', 'h.264', '0']:
            normalized_encoding = 'H264'
        elif input_value.lower() in ['h265', 'h.265', 'hevc', '1']:
            normalized_encoding = 'H265'
        elif input_value in ['H.264']:
            normalized_encoding = 'H264'
        elif input_value in ['H.265']:
            normalized_encoding = 'H265'
        else:
            normalized_encoding = 'UNKNOWN'
        
        status = "✓" if normalized_encoding == expected_output else "✗"
        print(f"  {status} Input: '{input_value}' -> Output: '{normalized_encoding}' (Expected: '{expected_output}')")

if __name__ == "__main__":
    test_video_encoding_mapping()
    print("\nVideo encoding mapping test completed!")

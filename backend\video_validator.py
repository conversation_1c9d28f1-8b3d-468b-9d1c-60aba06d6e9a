"""
Video file validation utility for the VMS archive system.
Provides functions to validate video file integrity and compatibility.
"""

import subprocess
import json
import logging
from pathlib import Path
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)

class VideoValidator:
    """Utility class for validating video files"""
    
    @staticmethod
    def get_video_info(file_path: Path) -> Optional[Dict]:
        """
        Get detailed video file information using ffprobe
        
        Args:
            file_path: Path to the video file
            
        Returns:
            Dictionary with video information or None if failed
        """
        try:
            if not file_path.exists():
                logger.error(f"Video file does not exist: {file_path}")
                return None
                
            # Use ffprobe to get video information
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                str(file_path)
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                logger.error(f"ffprobe failed for {file_path}: {result.stderr}")
                return None
                
            info = json.loads(result.stdout)
            return VideoValidator._parse_video_info(info, file_path)
            
        except subprocess.TimeoutExpired:
            logger.error(f"ffprobe timeout for {file_path}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse ffprobe output for {file_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting video info for {file_path}: {e}")
            return None
    
    @staticmethod
    def _parse_video_info(ffprobe_data: Dict, file_path: Path) -> Dict:
        """Parse ffprobe output into useful information"""
        info = {
            'file_path': str(file_path),
            'file_size': file_path.stat().st_size,
            'is_valid': False,
            'has_video': False,
            'has_audio': False,
            'duration': 0,
            'video_codec': None,
            'audio_codec': None,
            'container_format': None,
            'width': 0,
            'height': 0,
            'frame_rate': 0,
            'bit_rate': 0,
            'issues': []
        }
        
        try:
            # Parse format information
            if 'format' in ffprobe_data:
                format_info = ffprobe_data['format']
                info['container_format'] = format_info.get('format_name', 'unknown')
                info['duration'] = float(format_info.get('duration', 0))
                info['bit_rate'] = int(format_info.get('bit_rate', 0))
            
            # Parse stream information
            if 'streams' in ffprobe_data:
                for stream in ffprobe_data['streams']:
                    codec_type = stream.get('codec_type')
                    
                    if codec_type == 'video':
                        info['has_video'] = True
                        info['video_codec'] = stream.get('codec_name')
                        info['width'] = int(stream.get('width', 0))
                        info['height'] = int(stream.get('height', 0))
                        
                        # Parse frame rate
                        r_frame_rate = stream.get('r_frame_rate', '0/1')
                        if '/' in r_frame_rate:
                            num, den = r_frame_rate.split('/')
                            if int(den) > 0:
                                info['frame_rate'] = float(num) / float(den)
                    
                    elif codec_type == 'audio':
                        info['has_audio'] = True
                        info['audio_codec'] = stream.get('codec_name')
            
            # Validate the video file
            info['is_valid'] = VideoValidator._validate_video_info(info)
            
        except Exception as e:
            logger.error(f"Error parsing video info: {e}")
            info['issues'].append(f"Failed to parse video information: {e}")
        
        return info
    
    @staticmethod
    def _validate_video_info(info: Dict) -> bool:
        """Validate video file information and identify issues"""
        is_valid = True
        issues = info['issues']
        
        # Check file size
        if info['file_size'] == 0:
            issues.append("File is empty")
            is_valid = False
        elif info['file_size'] < 1024:  # Less than 1KB
            issues.append("File is suspiciously small")
            is_valid = False
        
        # Check duration
        if info['duration'] == 0:
            issues.append("No duration information")
            is_valid = False
        elif info['duration'] < 1:  # Less than 1 second
            issues.append("Duration is too short")
        
        # Check video stream
        if not info['has_video']:
            issues.append("No video stream found")
            is_valid = False
        else:
            # Check video codec compatibility
            compatible_video_codecs = ['h264', 'avc', 'x264']
            if info['video_codec'] not in compatible_video_codecs:
                issues.append(f"Video codec '{info['video_codec']}' may not be web-compatible")
            
            # Check resolution
            if info['width'] == 0 or info['height'] == 0:
                issues.append("Invalid video resolution")
                is_valid = False
            
            # Check frame rate
            if info['frame_rate'] == 0:
                issues.append("No frame rate information")
            elif info['frame_rate'] > 60:
                issues.append("Frame rate is unusually high")
        
        # Check audio stream
        if info['has_audio']:
            compatible_audio_codecs = ['aac', 'mp3']
            if info['audio_codec'] not in compatible_audio_codecs:
                issues.append(f"Audio codec '{info['audio_codec']}' may not be web-compatible")
        
        # Check container format
        if info['container_format']:
            if 'mp4' not in info['container_format'].lower():
                issues.append(f"Container format '{info['container_format']}' may not be web-compatible")
        
        return is_valid
    
    @staticmethod
    def validate_recording_file(file_path: Path) -> Dict:
        """
        Validate a recording file and return comprehensive information
        
        Args:
            file_path: Path to the recording file
            
        Returns:
            Dictionary with validation results
        """
        logger.info(f"Validating recording file: {file_path}")
        
        result = {
            'file_path': str(file_path),
            'exists': file_path.exists(),
            'is_valid': False,
            'is_playable': False,
            'info': None,
            'recommendations': []
        }
        
        if not result['exists']:
            result['recommendations'].append("File does not exist")
            return result
        
        # Get video information
        info = VideoValidator.get_video_info(file_path)
        result['info'] = info
        
        if info:
            result['is_valid'] = info['is_valid']
            result['is_playable'] = (
                info['has_video'] and 
                info['duration'] > 0 and 
                info['file_size'] > 1024
            )
            
            # Generate recommendations
            if not result['is_playable']:
                if not info['has_video']:
                    result['recommendations'].append("Re-encode with proper video codec (H.264)")
                if info['duration'] == 0:
                    result['recommendations'].append("File may be corrupted - check recording process")
                if info['file_size'] < 1024:
                    result['recommendations'].append("File is too small - check recording settings")
            
            if info['issues']:
                result['recommendations'].extend(info['issues'])
        else:
            result['recommendations'].append("Unable to analyze file - may be corrupted")
        
        return result
    
    @staticmethod
    def validate_recordings_directory(recordings_path: Path) -> Dict:
        """
        Validate all recording files in a directory
        
        Args:
            recordings_path: Path to the recordings directory
            
        Returns:
            Dictionary with validation results for all files
        """
        logger.info(f"Validating recordings directory: {recordings_path}")
        
        results = {
            'directory': str(recordings_path),
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'file_results': []
        }
        
        if not recordings_path.exists():
            logger.error(f"Recordings directory does not exist: {recordings_path}")
            return results
        
        # Find all MP4 files
        mp4_files = list(recordings_path.rglob('*.mp4'))
        results['total_files'] = len(mp4_files)
        
        for mp4_file in mp4_files:
            file_result = VideoValidator.validate_recording_file(mp4_file)
            results['file_results'].append(file_result)
            
            if file_result['is_valid']:
                results['valid_files'] += 1
            else:
                results['invalid_files'] += 1
        
        logger.info(f"Validation complete: {results['valid_files']}/{results['total_files']} files valid")
        return results


def main():
    """Command-line interface for video validation"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate VMS recording files')
    parser.add_argument('path', help='Path to video file or recordings directory')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # Set up logging
    level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=level, format='%(levelname)s: %(message)s')
    
    path = Path(args.path)
    
    if path.is_file():
        # Validate single file
        result = VideoValidator.validate_recording_file(path)
        print(json.dumps(result, indent=2))
    elif path.is_dir():
        # Validate directory
        result = VideoValidator.validate_recordings_directory(path)
        print(json.dumps(result, indent=2))
    else:
        print(f"Error: Path does not exist: {path}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FilterCard from './FilterCard';

describe('FilterCard Component', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  describe('Select Type Filter', () => {
    const selectProps = {
      title: 'Test Filter',
      type: 'select',
      value: 'option1',
      onChange: mockOnChange,
      options: [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' }
      ],
      isActive: false
    };

    test('renders select filter correctly', () => {
      render(<FilterCard {...selectProps} />);

      expect(screen.getByText('Test Filter')).toBeInTheDocument();
      expect(screen.getByText('Option 1')).toBeInTheDocument();
    });

    test('opens dropdown when clicked', async () => {
      render(<FilterCard {...selectProps} />);

      const selectButton = screen.getByRole('button', { name: /test filter filter/i });
      fireEvent.click(selectButton);

      await waitFor(() => {
        expect(screen.getByText('Option 2')).toBeInTheDocument();
        expect(screen.getByText('Option 3')).toBeInTheDocument();
      });
    });

    test('calls onChange when option is selected', async () => {
      render(<FilterCard {...selectProps} />);

      const selectButton = screen.getByRole('button', { name: /test filter filter/i });
      fireEvent.click(selectButton);

      await waitFor(() => {
        const option2 = screen.getByText('Option 2');
        fireEvent.click(option2);
      });

      expect(mockOnChange).toHaveBeenCalledWith('option2');
    });

    test('handles keyboard navigation', () => {
      render(<FilterCard {...selectProps} />);

      const selectButton = screen.getByRole('button', { name: /test filter filter/i });

      // Test Enter key
      fireEvent.keyDown(selectButton, { key: 'Enter' });
      expect(screen.getByText('Option 2')).toBeInTheDocument();

      // Test Escape key
      fireEvent.keyDown(selectButton, { key: 'Escape' });
      expect(screen.queryByText('Option 2')).not.toBeInTheDocument();
    });

    test('shows active state correctly', () => {
      render(<FilterCard {...selectProps} isActive={true} />);

      const filterCard = screen.getByText('Test Filter').closest('.filter-card');
      expect(filterCard).toHaveClass('active');
    });
  });

  describe('Input Type Filter', () => {
    const inputProps = {
      title: 'Search Filter',
      type: 'input',
      value: 'test value',
      onChange: mockOnChange,
      placeholder: 'Enter search term...',
      isActive: true
    };

    test('renders input filter correctly', () => {
      render(<FilterCard {...inputProps} />);

      expect(screen.getByText('Search Filter')).toBeInTheDocument();
      expect(screen.getByDisplayValue('test value')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter search term...')).toBeInTheDocument();
    });

    test('calls onChange when input value changes', () => {
      render(<FilterCard {...inputProps} />);

      const input = screen.getByDisplayValue('test value');
      fireEvent.change(input, { target: { value: 'new value' } });

      expect(mockOnChange).toHaveBeenCalledWith('new value');
    });

    test('shows active indicator when active', () => {
      render(<FilterCard {...inputProps} />);

      const filterCard = screen.getByText('Search Filter').closest('.filter-card');
      expect(filterCard).toHaveClass('active');

      const indicator = filterCard.querySelector('.filter-card-indicator');
      expect(indicator).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes for select', () => {
      const selectProps = {
        title: 'Accessible Filter',
        type: 'select',
        value: 'option1',
        onChange: mockOnChange,
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ]
      };

      render(<FilterCard {...selectProps} />);

      const selectButton = screen.getByRole('button', { name: /accessible filter filter/i });
      expect(selectButton).toHaveAttribute('aria-expanded', 'false');
      expect(selectButton).toHaveAttribute('aria-haspopup', 'listbox');
    });

    test('has proper ARIA attributes for input', () => {
      const inputProps = {
        title: 'Accessible Input',
        type: 'input',
        value: '',
        onChange: mockOnChange,
        placeholder: 'Search...'
      };

      render(<FilterCard {...inputProps} />);

      const input = screen.getByRole('textbox', { name: /accessible input filter input/i });
      expect(input).toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    test('handles disabled state correctly', () => {
      const disabledProps = {
        title: 'Disabled Filter',
        type: 'select',
        value: 'option1',
        onChange: mockOnChange,
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ],
        disabled: true
      };

      render(<FilterCard {...disabledProps} />);

      const filterCard = screen.getByText('Disabled Filter').closest('.filter-card');
      expect(filterCard).toHaveClass('disabled');

      const selectButton = screen.getByRole('button');
      expect(selectButton).toBeDisabled();
    });
  });

  describe('Compact Design', () => {
    test('renders with compact styling', () => {
      const compactProps = {
        title: 'Compact Filter',
        type: 'select',
        value: 'option1',
        onChange: mockOnChange,
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ]
      };

      render(<FilterCard {...compactProps} />);

      const filterCard = screen.getByText('Compact Filter').closest('.filter-card');
      expect(filterCard).toHaveClass('filter-card');

      // Check that the card has compact styling
      const computedStyle = window.getComputedStyle(filterCard);
      expect(computedStyle.minHeight).toBe('80px');
    });

    test('maintains functionality in compact mode', () => {
      const compactProps = {
        title: 'Compact Filter',
        type: 'input',
        value: '',
        onChange: mockOnChange,
        placeholder: 'Compact input...'
      };

      render(<FilterCard {...compactProps} />);

      const input = screen.getByPlaceholderText('Compact input...');
      fireEvent.change(input, { target: { value: 'test' } });

      expect(mockOnChange).toHaveBeenCalledWith('test');
    });
  });
});

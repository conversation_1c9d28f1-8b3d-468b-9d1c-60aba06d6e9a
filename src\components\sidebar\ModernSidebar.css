/*
 * ModernSidebar.css
 * Modern styling for the VMS application sidebar
 */

/* Animation Keyframes */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Base Sidebar Container */
.sidebar {
  width: 256px; /* 16rem = 256px */
  background: linear-gradient(135deg, #00299d 0%, #000000 100%); /* Blue to black gradient */
  color: white;
  padding: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  position: fixed;
  left: 0;
  top: 0; /* Start from the top of the page */
  overflow-y: auto;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 41, 157, 0.3);
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  animation: slideIn 0.5s ease-out forwards;
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.sidebar-logo {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.sidebar-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.sidebar-logo:hover img {
  transform: scale(1.05);
}

.sidebar-header-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #000000;
  text-shadow: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes shine {
  to {
    background-position: 200% center;
  }
}

.sidebar-content {
  flex: 1;
  padding: 0 0 24px 0;
  overflow-y: auto;
}

/* Sidebar Section */
.sidebar-section {
  margin-bottom: 16px;
  padding: 0 16px;
}

/* Group Headers - add more space between major sections */
.sidebar-section-header {
  margin-top: 32px;
  margin-bottom: 16px;
  padding: 0 16px;
  font-weight: 600;
  font-size: 14px;
  color: #A0A3A6; /* secondary text color */
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Sidebar Button - Main Navigation Items */
.sidebar-btn {
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  color: #000000;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  margin-bottom: 0.375rem;
  overflow: hidden;
  transition: all 0.3s ease;
  transform-origin: left center;
}

/* Hover State */
.sidebar-btn:hover {
  transform: scale(1.05);
  background-color: transparent;
}

/* Active State */
.sidebar-btn.active {
  background-color: rgba(0, 0, 0, 0.15);
  border-left: 4px solid #000000;
  font-weight: 600;
}

/* Focus State for Accessibility */
.sidebar-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.3);
}

/* Sidebar Icon Container */
.sidebar-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* Create a container around the icon */
.sidebar-btn .sidebar-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
}

.sidebar-btn:hover .sidebar-icon {
  transform: scale(1.1);
}

.sidebar-btn.active .sidebar-icon {
  background-color: rgba(0, 0, 0, 0.15);
}

/* Dropdown Container */
.sidebar-dropdown {
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0.25rem 0 0.5rem 0.5rem;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 0.5rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dropdown Item */
.dropdown-item {
  padding: 0.5rem 0.75rem 0.5rem 2.5rem; /* Extra left padding to indent */
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #000000;
  font-size: 0.8125rem;
  font-weight: 400;
  position: relative;
  margin: 0.125rem 0;
  border-radius: 0.375rem;
}

.dropdown-item:hover {
  background-color: transparent;
  transform: scale(1.05);
}

.dropdown-item:hover {
  background-color: transparent;
  color: #000000;
  transform: translateX(3px);
}

.dropdown-item:hover::before {
  background-color: transparent;
  box-shadow: none;
}

.dropdown-item.active {
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.02));
  color: #000000;
  font-weight: 500;
}

.dropdown-item.active::before {
  background-color: #000000;
  width: 6px;
  height: 6px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* Sub-menu Button */
.sidebar-btn-sub {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
  font-size: 0.8125rem;
  font-weight: 400;
  color: #000000;
  background: transparent;
  padding-left: 2.5rem;
  position: relative;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  transform-origin: left center;
  width: 100%;
  text-align: left;
}

.sidebar-btn-sub:hover {
  background-color: transparent;
  transform: scale(1.05);
}

.sidebar-btn-sub.active {
  background-color: rgba(0, 0, 0, 0.15);
  border-left: 4px solid #000000;
  font-weight: 500;
}

/* Counts and Badges */
.collection-count,
.bookmark-count {
  margin-left: auto;
  font-size: 0.75rem;
  background-color: rgba(0, 0, 0, 0.1);
  color: #000000;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.sidebar-btn:hover .collection-count,
.sidebar-btn:hover .bookmark-count {
  background-color: transparent;
  transform: scale(1.05);
}

.sidebar-btn.active .collection-count,
.sidebar-btn.active .bookmark-count {
  background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}

/* Notification Dot */
.notification-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  margin-left: auto;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.6);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

/* Chevron for expandable sections */
.chevron-icon {
  margin-left: auto;
  transition: transform 0.3s ease;
  font-size: 0.75rem;
}

.sidebar-btn:hover .chevron-icon {
  color: #000000;
}

.chevron-icon.expanded {
  transform: rotate(180deg);
}

/* Responsive Design - Mobile View */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
    transition: width 0.3s ease;
  }

  .sidebar.expanded {
    width: 256px;
  }

  .sidebar-header {
    padding: 1rem;
    justify-content: center;
  }

  .sidebar-header-title {
    display: none;
  }

  .sidebar.expanded .sidebar-header {
    padding: 1.5rem;
    justify-content: flex-start;
  }

  .sidebar.expanded .sidebar-header-title {
    display: block;
  }

  .sidebar-btn {
    justify-content: center;
    padding: 0.75rem;
  }

  .sidebar-btn span,
  .sidebar-btn .chevron-icon,
  .sidebar-btn .collection-count,
  .sidebar-btn .bookmark-count {
    display: none;
  }

  .sidebar.expanded .sidebar-btn {
    justify-content: flex-start;
  }

  .sidebar.expanded .sidebar-btn span,
  .sidebar.expanded .sidebar-btn .chevron-icon,
  .sidebar.expanded .sidebar-btn .collection-count,
  .sidebar.expanded .sidebar-btn .bookmark-count {
    display: inline-flex;
  }

  .sidebar-dropdown {
    display: none;
  }

  .sidebar.expanded .sidebar-dropdown {
    display: block;
  }

  /* Hamburger menu for mobile */
  .sidebar-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1020;
    background: linear-gradient(to bottom, #32116d8c, #672491);
    border-radius: 50%;
    color: #FFFFFF;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    font-size: 1.25rem;
  }

  .sidebar-toggle:hover {
    transform: scale(1.1);
  }
}

/* Tooltip for collapsed sidebar */
.sidebar-tooltip {
  position: absolute;
  left: 70px;
  background-color: #9b1fe8;
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  z-index: 1020;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateX(-10px);
  font-weight: 500;
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -6px;
  transform: translateY(-50%) rotate(45deg);
  width: 12px;
  height: 12px;
  background-color: #9b1fe8;
}

.sidebar-btn:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* Accessibility */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
